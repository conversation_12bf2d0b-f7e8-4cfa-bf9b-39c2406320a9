import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import 'package:wiffada/models/product_model.dart';
import 'package:wiffada/providers/products_provider.dart';
import 'package:wiffada/providers/language_provider.dart';
import 'package:wiffada/utils/app_localizations.dart';
import 'package:wiffada/widgets/custom_snackbar.dart';
import 'package:iconsax/iconsax.dart';
import 'package:flutter_animate/flutter_animate.dart';

class CartPage extends StatelessWidget {
  const CartPage({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final translations = AppLocalizations(languageProvider.currentLanguage);
    final productsProvider = Provider.of<ProductsProvider>(context);
    final cartItems = productsProvider.cartItems;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        toolbarHeight: 70,
        backgroundColor: const Color(0xFF4F908E),
        elevation: 0,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            bottom: Radius.circular(25),
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF4F908E),
                const Color(0xFF4F908E).withOpacity(0.8),
              ],
            ),
            borderRadius: const BorderRadius.vertical(
              bottom: Radius.circular(25),
            ),
          ),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Iconsax.shopping_cart,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              translations.translate('cart'),
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
                shadows: [
                  Shadow(
                    color: Colors.black.withOpacity(0.2),
                    offset: const Offset(0, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
            ),
          ],
        ),
        leading: Container(
          margin: const EdgeInsets.only(left: 8),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            shape: BoxShape.circle,
          ),
          child: IconButton(
            icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white, size: 18),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        actions: [
          if (cartItems.isNotEmpty)
            Container(
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: const Icon(Icons.delete_outline, color: Colors.white, size: 20),
                onPressed: () {
                  _showClearCartDialog(context, translations, productsProvider);
                },
              ),
            ),
        ],
      ),
      body: cartItems.isEmpty
          ? _buildEmptyCart(translations)
          : _buildCartList(cartItems, translations, productsProvider),
      bottomNavigationBar: cartItems.isEmpty
          ? null
          : _buildCheckoutBar(context, translations, productsProvider),
    );
  }

  Widget _buildEmptyCart(AppLocalizations translations) {
    return Builder(
      builder: (context) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // صورة السلة الفارغة
            Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                color: const Color(0xFF4F908E).withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Iconsax.shopping_cart,
                size: 80,
                color: Color(0xFF4F908E),
              ),
            ).animate().fadeIn().scale(
                  begin: const Offset(0.8, 0.8),
                  end: const Offset(1, 1),
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.elasticOut,
                ),
            const SizedBox(height: 32),
            // نص السلة فارغة
            Text(
              translations.translate('cart_empty'),
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ).animate().fadeIn(delay: const Duration(milliseconds: 200)),
            const SizedBox(height: 12),
            // نص إضافي
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                translations.translate('add_items_to_cart'),
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 16,
                  color: Colors.grey[600],
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ).animate().fadeIn(delay: const Duration(milliseconds: 300)),
            const SizedBox(height: 40),
            // زر متابعة التسوق
            SizedBox(
              width: 220,
              height: 50,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.pop(context);
                },
                icon: const Icon(Iconsax.shop, size: 20),
                label: Text(
                  translations.translate('continue_shopping'),
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4F908E),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  elevation: 2,
                  shadowColor: const Color(0xFF4F908E).withOpacity(0.3),
                ),
              ),
            ).animate().fadeIn(delay: const Duration(milliseconds: 400)).slideY(
                  begin: 0.2,
                  end: 0,
                  delay: const Duration(milliseconds: 400),
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                ),
          ],
        ),
      ),
    );
  }

  Widget _buildCartList(List<Product> cartItems, AppLocalizations translations, ProductsProvider productsProvider) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: cartItems.length,
      itemBuilder: (context, index) {
        final product = cartItems[index];
        return _buildCartItem(product, translations, productsProvider, context);
      },
    );
  }

  Widget _buildCartItem(Product product, AppLocalizations translations, ProductsProvider productsProvider, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Dismissible(
        key: Key(product.id),
        direction: DismissDirection.endToStart,
        background: Container(
          alignment: Alignment.centerRight,
          padding: const EdgeInsets.only(right: 20),
          decoration: BoxDecoration(
            color: Colors.red.withOpacity(0.8),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Icon(
            Icons.delete,
            color: Colors.white,
            size: 28,
          ),
        ),
        onDismissed: (direction) {
          productsProvider.removeFromCart(product);
          CustomSnackbar.show(
            context: context,
            message: translations.translate('item_removed'),
            icon: Icons.remove_shopping_cart,
            backgroundColor: Colors.red,
          );
        },
        child: Row(
          children: [
            // صورة المنتج
            Hero(
              tag: 'cart-image-${product.id}',
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    bottomLeft: Radius.circular(20),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    bottomLeft: Radius.circular(20),
                  ),
                  child: CachedNetworkImage(
                    imageUrl: product.imageUrl,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Shimmer.fromColors(
                      baseColor: Colors.grey[300]!,
                      highlightColor: Colors.grey[100]!,
                      child: Container(
                        color: Colors.white,
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[200],
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(Icons.error_outline, color: Colors.grey, size: 30),
                          const SizedBox(height: 4),
                          Text(
                            'صورة غير متوفرة',
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: 10,
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
            // معلومات المنتج
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم المنتج
                    Text(
                      product.name,
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 8),
                    // تفاصيل المنتج (الحجم واللون)
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        if (product.sizes.isNotEmpty)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              product.sizes[0],
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 11,
                                color: Colors.grey[700],
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        if (product.colors.isNotEmpty)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.grey[100],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              product.colors[0],
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 11,
                                color: Colors.grey[700],
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    // السعر
                    Text(
                      '${product.price.toStringAsFixed(2)} ${translations.translate('sar')}',
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF4F908E),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // زر الحذف
            Container(
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: const Icon(Icons.delete_outline, color: Colors.red, size: 22),
                onPressed: () {
                  productsProvider.removeFromCart(product);
                  CustomSnackbar.show(
                    context: context,
                    message: translations.translate('item_removed'),
                    icon: Icons.remove_shopping_cart,
                    backgroundColor: Colors.red,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    ).animate().fadeIn().slideX(begin: 0.1, duration: const Duration(milliseconds: 300));
  }

  Widget _buildCheckoutBar(BuildContext context, AppLocalizations translations, ProductsProvider productsProvider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(30),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 15,
            offset: const Offset(0, -5),
            spreadRadius: 1,
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // ملخص السلة
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${productsProvider.cartItemCount} ${translations.translate('items')}',
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 14,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: [
                    Text(
                      translations.translate('total'),
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 14,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${productsProvider.cartTotal.toStringAsFixed(2)} ${translations.translate('sar')}',
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF4F908E),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            // زر إتمام الشراء
            SizedBox(
              width: double.infinity,
              height: 55,
              child: ElevatedButton.icon(
                onPressed: () {
                  _showCheckoutDialog(context, translations);
                },
                icon: const Icon(Iconsax.shopping_bag, size: 20),
                label: Text(
                  translations.translate('checkout'),
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4F908E),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                  elevation: 0,
                ),
              ),
            ).animate().fadeIn().slideY(begin: 0.2, duration: const Duration(milliseconds: 300)),
          ],
        ),
      ),
    );
  }

  void _showClearCartDialog(BuildContext context, AppLocalizations translations, ProductsProvider productsProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          translations.translate('clear_cart'),
          style: GoogleFonts.ibmPlexSansArabic(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          translations.translate('clear_cart_confirm'),
          style: GoogleFonts.ibmPlexSansArabic(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              translations.translate('cancel'),
              style: GoogleFonts.ibmPlexSansArabic(
                color: Colors.grey[700],
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              productsProvider.clearCart();
              Navigator.pop(context);
              CustomSnackbar.show(
                context: context,
                message: translations.translate('cart_cleared'),
                icon: Icons.shopping_cart,
              );
            },
            child: Text(
              translations.translate('clear'),
              style: GoogleFonts.ibmPlexSansArabic(
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showCheckoutDialog(BuildContext context, AppLocalizations translations) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          translations.translate('checkout'),
          style: GoogleFonts.ibmPlexSansArabic(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          translations.translate('checkout_coming_soon'),
          style: GoogleFonts.ibmPlexSansArabic(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              translations.translate('ok'),
              style: GoogleFonts.ibmPlexSansArabic(
                color: const Color(0xFF4F908E),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
