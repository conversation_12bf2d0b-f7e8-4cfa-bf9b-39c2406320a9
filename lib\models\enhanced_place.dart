import 'package:google_maps_flutter/google_maps_flutter.dart';

class EnhancedPlace {
  final String id;
  final String title;
  final String shortDescription;
  final String description;
  final LatLng location;
  final String category;
  final List<String> images;
  final MediaContent? video;
  final MediaContent? tourGuide;
  final MediaContent? sound;
  final String? locationUrl;
  final PlaceCategory categoryInfo;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  EnhancedPlace({
    required this.id,
    required this.title,
    required this.shortDescription,
    required this.description,
    required this.location,
    required this.category,
    required this.images,
    this.video,
    this.tourGuide,
    this.sound,
    this.locationUrl,
    required this.categoryInfo,
    this.createdAt,
    this.updatedAt,
  });

  factory EnhancedPlace.fromJson(Map<String, dynamic> json) {
    // استخراج الإحداثيات من رابط Google Maps
    LatLng location = const LatLng(24.4672, 39.6117); // المدينة المنورة افتراضي

    if (json['location'] != null && json['location'].toString().isNotEmpty) {
      final locationStr = json['location'].toString();
      if (locationStr.contains('maps')) {
        // استخراج الإحداثيات من رابط Google Maps
        final regex = RegExp(r'!3d(-?\d+\.?\d*)!4d(-?\d+\.?\d*)');
        final match = regex.firstMatch(locationStr);
        if (match != null) {
          final lat = double.tryParse(match.group(1) ?? '');
          final lng = double.tryParse(match.group(2) ?? '');
          if (lat != null && lng != null) {
            location = LatLng(lat, lng);
          }
        }
      }
    }

    return EnhancedPlace(
      id: json['_id'] ?? '',
      title: json['title']?['ar'] ?? 'غير محدد',
      shortDescription: json['short_description']?['ar'] ?? '',
      description: json['description']?['ar'] ?? '',
      location: location,
      category: json['category_slug'] ?? 'general',
      images: List<String>.from(json['images'] ?? []),
      video: json['video']?['ar'] != null
          ? MediaContent.fromUrl(json['video']['ar'])
          : null,
      tourGuide: json['tour_guide']?['ar'] != null
          ? MediaContent.fromUrl(json['tour_guide']['ar'])
          : null,
      sound: json['sound']?['ar'] != null
          ? MediaContent.fromUrl(json['sound']['ar'])
          : null,
      locationUrl: json['location'],
      categoryInfo: PlaceCategory.fromJson(json['category'] ?? {}),
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : null,
    );
  }

  // الحصول على الصورة الرئيسية
  String get primaryImage {
    if (images.isNotEmpty) {
      return images.first;
    }
    // استخدام صورة محلية حسب الفئة
    switch (category) {
      case 'prophets-holy-osque':
        return 'assets/images/places/mosque3601.jpg';
      case 'historical':
        return 'assets/images/places/quba1.jpg';
      default:
        return 'assets/images/places/rawdah1.jpg';
    }
  }

  // الحصول على الصورة المحلية المناسبة
  String get localImage {
    switch (title) {
      case 'الحجرة النبوية':
        return 'assets/images/places/rawdah1.jpg';
      case 'الروضة الشريفة':
        return 'assets/images/places/rawdah2.jpg';
      case 'بقيع الغرقد':
        return 'assets/images/places/baqee1.jpg';
      case 'مكتبة الحرم النبوي':
        return 'assets/images/places/mosque3601.jpg';
      case 'المحراب النبوي':
        return 'assets/images/places/mihrab1.jpg';
      case 'المنبر النبوي':
        return 'assets/images/places/minbar1.jpg';
      case 'الأعمدة التاريخية':
        return 'assets/images/places/columns1.jpg';
      case 'مسجد قباء':
        return 'assets/images/places/quba1.jpg';
      case 'مسجد القبلتين':
        return 'assets/images/places/qiblatain1.jpg';
      default:
        return 'assets/images/places/mosque3601.jpg';
    }
  }

  // التحقق من وجود محتوى وسائط
  bool get hasVideo => video != null;
  bool get hasTourGuide => tourGuide != null;
  bool get hasSound => sound != null;
  bool get hasMultimedia => hasVideo || hasTourGuide || hasSound;

  // الحصول على عدد الوسائط المتاحة
  int get mediaCount {
    int count = 0;
    if (hasVideo) count++;
    if (hasTourGuide) count++;
    if (hasSound) count++;
    return count;
  }
}

class MediaContent {
  final String url;
  final MediaType type;
  final String? title;
  final Duration? duration;

  MediaContent({
    required this.url,
    required this.type,
    this.title,
    this.duration,
  });

  factory MediaContent.fromUrl(String url) {
    MediaType type;
    if (url.contains('.mp4') ||
        url.contains('youtube') ||
        url.contains('panoraven')) {
      type = MediaType.video;
    } else if (url.contains('.mp3') || url.contains('audio')) {
      type = MediaType.audio;
    } else {
      type = MediaType.unknown;
    }

    return MediaContent(
      url: url,
      type: type,
    );
  }

  bool get isVideo => type == MediaType.video;
  bool get isAudio => type == MediaType.audio;
  bool get isPanorama => url.contains('panoraven');
  bool get isYoutube => url.contains('youtube');
}

enum MediaType {
  video,
  audio,
  image,
  unknown,
}

class PlaceCategory {
  final String id;
  final String name;
  final String slug;

  PlaceCategory({
    required this.id,
    required this.name,
    required this.slug,
  });

  factory PlaceCategory.fromJson(Map<String, dynamic> json) {
    return PlaceCategory(
      id: json['_id'] ?? '',
      name: json['name']?['ar'] ?? 'عام',
      slug: json['slug']?['ar'] ?? 'general',
    );
  }
}

// فئات الأماكن المحسنة مع الألوان والأيقونات
class EnhancedPlaceCategory {
  final String id;
  final String name;
  final String icon;
  final String color;
  final List<String> gradient;

  EnhancedPlaceCategory({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
    required this.gradient,
  });

  // إنشاء فئة من بيانات JSON
  static EnhancedPlaceCategory fromCategorySlug(String slug, String name) {
    // تحديد الألوان والأيقونات حسب slug الفئة
    switch (slug) {
      case 'prophets-holy-osque':
      case 'prophet\'s-holy-mosque':
        return EnhancedPlaceCategory(
          id: slug,
          name: name,
          icon: '🕌',
          color: '#2E8B57',
          gradient: ['#2E8B57', '#3CB371'],
        );
      case 'historical-sites':
      case 'historical':
        return EnhancedPlaceCategory(
          id: slug,
          name: name,
          icon: '🏛️',
          color: '#D4A574',
          gradient: ['#D4A574', '#DEB887'],
        );
      case 'mosques':
        return EnhancedPlaceCategory(
          id: slug,
          name: name,
          icon: '🕌',
          color: '#6A5ACD',
          gradient: ['#6A5ACD', '#7B68EE'],
        );
      case 'museums':
      case 'libraries':
        return EnhancedPlaceCategory(
          id: slug,
          name: name,
          icon: '📚',
          color: '#4169E1',
          gradient: ['#4169E1', '#6495ED'],
        );
      case 'mountains':
      case 'hills':
        return EnhancedPlaceCategory(
          id: slug,
          name: name,
          icon: '⛰️',
          color: '#8B4513',
          gradient: ['#8B4513', '#A0522D'],
        );
      case 'markets':
      case 'shopping':
        return EnhancedPlaceCategory(
          id: slug,
          name: name,
          icon: '🏪',
          color: '#FF6B35',
          gradient: ['#FF6B35', '#FF8C42'],
        );
      case 'hotels':
      case 'accommodation':
        return EnhancedPlaceCategory(
          id: slug,
          name: name,
          icon: '🏨',
          color: '#9B59B6',
          gradient: ['#9B59B6', '#8E44AD'],
        );
      default:
        return EnhancedPlaceCategory(
          id: slug,
          name: name,
          icon: '📍',
          color: '#4F908E',
          gradient: ['#4F908E', '#6BA3A0'],
        );
    }
  }

  static List<EnhancedPlaceCategory> getDefaultCategories() {
    return [
      EnhancedPlaceCategory(
        id: 'all',
        name: 'الكل',
        icon: '🌍',
        color: '#4F908E',
        gradient: ['#4F908E', '#6BA3A0'],
      ),
    ];
  }
}
