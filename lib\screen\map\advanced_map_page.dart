import 'dart:async';
import 'dart:ui';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:wiffada/utils/map_constants.dart';
import 'package:wiffada/models/place.dart';
import 'package:wiffada/providers/places_provider.dart';
import 'package:wiffada/screen/map/place_details_page.dart';
import 'package:wiffada/theme/app_colors.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax/iconsax.dart';

class AdvancedMapPage extends StatefulWidget {
  final String? initialFilter;

  const AdvancedMapPage({super.key, this.initialFilter});

  @override
  State<AdvancedMapPage> createState() => _AdvancedMapPageState();
}

class _AdvancedMapPageState extends State<AdvancedMapPage>
    with TickerProviderStateMixin {
  final Completer<GoogleMapController> _mapController = Completer();
  late AnimationController _animationController;
  late AnimationController _pulseController;
  late AnimationController _fabController;

  Set<Marker> _markers = {};
  final Set<Polyline> _polylines = {};
  Set<Circle> _circles = {};

  String _searchQuery = '';
  String _selectedCategory = 'all';
  bool _showSatellite = false;
  bool _show3D = true;
  final bool _showTraffic = false;
  bool _showHistoricalLayer = false;
  final bool _isNavigationMode = false;

  // طبقات الخريطة المختلفة
  final List<MapLayer> _mapLayers = [
    MapLayer(
      id: 'modern',
      name: 'الخريطة الحديثة',
      icon: Iconsax.map,
      color: const Color(0xFF4F908E),
      isActive: true,
    ),
    MapLayer(
      id: 'historical',
      name: 'الخريطة التاريخية',
      icon: Iconsax.clock,
      color: const Color(0xFFD4A574),
      isActive: false,
    ),
    MapLayer(
      id: 'pilgrimage',
      name: 'مسارات الحج',
      icon: Iconsax.route_square,
      color: const Color(0xFF8B5A3C),
      isActive: false,
    ),
    MapLayer(
      id: 'services',
      name: 'الخدمات',
      icon: Iconsax.hospital,
      color: const Color(0xFF2E8B57),
      isActive: false,
    ),
  ];

  // فئات الأماكن المحسنة
  final List<PlaceCategory> _categories = [
    PlaceCategory(
      id: 'all',
      name: 'الكل',
      icon: Iconsax.global,
      color: const Color(0xFF4F908E),
      gradient: [const Color(0xFF4F908E), const Color(0xFF6BA3A0)],
    ),
    PlaceCategory(
      id: 'mosques',
      name: 'المساجد المقدسة',
      icon: Iconsax.building_4,
      color: const Color(0xFF2E8B57),
      gradient: [const Color(0xFF2E8B57), const Color(0xFF3CB371)],
    ),
    PlaceCategory(
      id: 'historical',
      name: 'المعالم التاريخية',
      icon: Iconsax.crown,
      color: const Color(0xFFD4A574),
      gradient: [const Color(0xFFD4A574), const Color(0xFFDEB887)],
    ),
    PlaceCategory(
      id: 'mountains',
      name: 'الجبال المقدسة',
      icon: Iconsax.mountain,
      color: const Color(0xFF8B4513),
      gradient: [const Color(0xFF8B4513), const Color(0xFFA0522D)],
    ),
    PlaceCategory(
      id: 'museums',
      name: 'المتاحف',
      icon: Iconsax.book_1,
      color: const Color(0xFF6A5ACD),
      gradient: [const Color(0xFF6A5ACD), const Color(0xFF7B68EE)],
    ),
    PlaceCategory(
      id: 'hotels',
      name: 'الفنادق',
      icon: Iconsax.building,
      color: const Color(0xFF4169E1),
      gradient: [const Color(0xFF4169E1), const Color(0xFF6495ED)],
    ),
    PlaceCategory(
      id: 'restaurants',
      name: 'المطاعم',
      icon: Iconsax.cup,
      color: const Color(0xFFFF6347),
      gradient: [const Color(0xFFFF6347), const Color(0xFFFF7F50)],
    ),
    PlaceCategory(
      id: 'shopping',
      name: 'التسوق',
      icon: Iconsax.bag_2,
      color: const Color(0xFFFF1493),
      gradient: [const Color(0xFFFF1493), const Color(0xFFFF69B4)],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat();
    _fabController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadPlaces();
    });
  }

  Future<void> _loadPlaces() async {
    final provider = Provider.of<PlacesProvider>(context, listen: false);
    await provider.fetchPlaces();
    _updateMarkers(provider.places);
  }

  Future<void> _updateMarkers(List<Place> places) async {
    final markers = <Marker>{};
    final circles = <Circle>{};

    for (final place in places) {
      if (_selectedCategory == 'all' || place.category == _selectedCategory) {
        // إنشاء marker مخصص
        final icon = await _createCustomMarker(place);

        markers.add(
          Marker(
            markerId: MarkerId(place.id),
            position: place.location,
            icon: icon,
            onTap: () => _onMarkerTapped(place),
            infoWindow: InfoWindow(
              title: place.name,
              snippet: place.description,
            ),
          ),
        );

        // إضافة دائرة للأماكن المهمة
        if (place.category == 'mosques') {
          circles.add(
            Circle(
              circleId: CircleId('${place.id}_circle'),
              center: place.location,
              radius: 200,
              fillColor: const Color(0xFF2E8B57).withValues(alpha: 0.1),
              strokeColor: const Color(0xFF2E8B57).withValues(alpha: 0.3),
              strokeWidth: 2,
            ),
          );
        }
      }
    }

    setState(() {
      _markers = markers;
      _circles = circles;
    });
  }

  Future<BitmapDescriptor> _createCustomMarker(Place place) async {
    final category = _categories.firstWhere(
      (cat) => cat.id == place.category,
      orElse: () => _categories.first,
    );

    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(pictureRecorder);
    const size = Size(60, 80);

    // رسم الخلفية المتدرجة
    final gradient = LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: category.gradient,
    );

    final rect = Rect.fromLTWH(0, 0, size.width, size.height - 20);
    final paint = Paint()..shader = gradient.createShader(rect);

    // رسم شكل الـ marker
    final path = Path();
    path.addRRect(RRect.fromRectAndRadius(
      const Rect.fromLTWH(10, 10, 40, 40),
      const Radius.circular(20),
    ));
    path.moveTo(30, 50);
    path.lineTo(20, 65);
    path.lineTo(40, 65);
    path.close();

    canvas.drawPath(path, paint);

    // رسم الحدود
    final borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;
    canvas.drawPath(path, borderPaint);

    // رسم الأيقونة
    final iconPainter = TextPainter(textDirection: TextDirection.ltr);
    iconPainter.text = TextSpan(
      text: String.fromCharCode(category.icon.codePoint),
      style: TextStyle(
        fontSize: 20,
        fontFamily: category.icon.fontFamily,
        color: Colors.white,
        fontWeight: FontWeight.bold,
      ),
    );
    iconPainter.layout();
    iconPainter.paint(
      canvas,
      Offset(30 - iconPainter.width / 2, 30 - iconPainter.height / 2),
    );

    final picture = pictureRecorder.endRecording();
    final image =
        await picture.toImage(size.width.toInt(), size.height.toInt());
    final bytes = await image.toByteData(format: ImageByteFormat.png);

    return BitmapDescriptor.bytes(bytes!.buffer.asUint8List());
  }

  void _onMarkerTapped(Place place) {
    _showPlaceBottomSheet(place);
  }

  void _showPlaceBottomSheet(Place place) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildPlaceBottomSheet(place),
    );
  }

  Widget _buildPlaceBottomSheet(Place place) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // صورة المكان
                  Container(
                    height: 200,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          const Color(0xFF4F908E).withValues(alpha: 0.1),
                          const Color(0xFF4F908E).withValues(alpha: 0.3),
                        ],
                      ),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Icon(
                            Iconsax.building_4,
                            size: 60,
                            color: Color(0xFF4F908E),
                          ),
                          const SizedBox(height: 12),
                          Text(
                            place.name,
                            style: GoogleFonts.ibmPlexSansArabic(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF4F908E),
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 20),

                  // معلومات المكان
                  Text(
                    place.name,
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF2C3E50),
                    ),
                  ),

                  const SizedBox(height: 8),

                  Text(
                    place.description,
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 16,
                      color: Colors.grey[600],
                      height: 1.5,
                    ),
                  ),

                  const SizedBox(height: 20),

                  // أزرار الإجراءات
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _navigateToPlace(place),
                          icon:
                              const Icon(Iconsax.routing, color: Colors.white),
                          label: Text(
                            'التوجه إلى المكان',
                            style: GoogleFonts.ibmPlexSansArabic(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF4F908E),
                            padding: const EdgeInsets.symmetric(vertical: 15),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      ElevatedButton(
                        onPressed: () => _showPlaceDetails(place),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.grey[100],
                          padding: const EdgeInsets.all(15),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Icon(
                          Iconsax.info_circle,
                          color: Color(0xFF4F908E),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    ).animate().slideY(begin: 1, duration: const Duration(milliseconds: 300));
  }

  void _navigateToPlace(Place place) {
    // تنفيذ التوجه إلى المكان
    Navigator.pop(context);
    // يمكن إضافة منطق التوجه هنا
  }

  void _showPlaceDetails(Place place) {
    Navigator.pop(context);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PlaceDetailsPage(place: place),
      ),
    );
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController.complete(controller);
    _setMapStyle(controller);
  }

  void _setMapStyle(GoogleMapController controller) {
    if (_showHistoricalLayer) {
      // نمط تاريخي
      controller.setMapStyle('''
        [
          {
            "elementType": "geometry",
            "stylers": [{"color": "#f5f1e8"}]
          },
          {
            "elementType": "labels.text.fill",
            "stylers": [{"color": "#8B4513"}]
          },
          {
            "featureType": "water",
            "elementType": "geometry.fill",
            "stylers": [{"color": "#87CEEB"}]
          }
        ]
      ''');
    } else {
      // النمط الحديث
      controller.setMapStyle('''
        [
          {
            "elementType": "geometry",
            "stylers": [{"color": "#f0f4f5"}]
          },
          {
            "elementType": "labels.text.fill",
            "stylers": [{"color": "#4F908E"}]
          },
          {
            "featureType": "water",
            "elementType": "geometry.fill",
            "stylers": [{"color": "#8fd0e8"}]
          }
        ]
      ''');
    }
  }

  Future<void> _goToCurrentLocation() async {
    final controller = await _mapController.future;
    controller.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: MapConstants.MADINAH_CENTER,
          zoom: 15,
          tilt: _show3D ? 45 : 0,
        ),
      ),
    );
  }

  Future<void> _zoomIn() async {
    final controller = await _mapController.future;
    controller.animateCamera(CameraUpdate.zoomIn());
  }

  Future<void> _zoomOut() async {
    final controller = await _mapController.future;
    controller.animateCamera(CameraUpdate.zoomOut());
  }

  void _toggleMapType() {
    setState(() {
      _showSatellite = !_showSatellite;
    });
  }

  void _toggle3D() async {
    setState(() {
      _show3D = !_show3D;
    });
    final controller = await _mapController.future;
    controller.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: MapConstants.MADINAH_CENTER,
          zoom: 15,
          tilt: _show3D ? 45 : 0,
        ),
      ),
    );
  }

  void _toggleHistoricalLayer() {
    setState(() {
      _showHistoricalLayer = !_showHistoricalLayer;
    });
    _mapController.future.then((controller) {
      _setMapStyle(controller);
    });
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: TextField(
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
        decoration: InputDecoration(
          hintText: 'ابحث عن مكان في المدينة المنورة...',
          hintStyle: GoogleFonts.ibmPlexSansArabic(
            color: Colors.grey[400],
            fontSize: 14,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF4F908E).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Iconsax.search_normal,
              color: Color(0xFF4F908E),
              size: 20,
            ),
          ),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                  icon: const Icon(
                    Iconsax.close_circle,
                    color: Colors.grey,
                  ),
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 15,
          ),
        ),
        style: GoogleFonts.ibmPlexSansArabic(
          fontSize: 14,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildCategoriesSlider() {
    return Container(
      height: 120,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = category.id == _selectedCategory;

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedCategory = category.id;
              });
              final provider =
                  Provider.of<PlacesProvider>(context, listen: false);
              _updateMarkers(provider.places);
            },
            child: Container(
              width: 90,
              margin: const EdgeInsets.only(left: 12),
              child: Column(
                children: [
                  Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: isSelected
                            ? category.gradient
                            : [Colors.grey[200]!, Colors.grey[300]!],
                      ),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: isSelected
                          ? [
                              BoxShadow(
                                color: category.color.withValues(alpha: 0.3),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                              ),
                            ]
                          : null,
                    ),
                    child: Icon(
                      category.icon,
                      color: isSelected ? Colors.white : Colors.grey[600],
                      size: 28,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    category.name,
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 11,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.w500,
                      color: isSelected ? category.color : Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          )
              .animate()
              .fadeIn(delay: Duration(milliseconds: index * 100))
              .slideX(begin: 0.3, duration: const Duration(milliseconds: 400));
        },
      ),
    );
  }

  Widget _buildFloatingControls() {
    return Positioned(
      right: 16,
      bottom: 100,
      child: Column(
        children: [
          // زر التبديل بين 2D/3D
          FloatingActionButton(
            heroTag: "3d_toggle",
            mini: true,
            backgroundColor: _show3D ? const Color(0xFF4F908E) : Colors.white,
            onPressed: _toggle3D,
            child: Icon(
              Iconsax.box,
              color: _show3D ? Colors.white : const Color(0xFF4F908E),
            ),
          ).animate().scale(delay: const Duration(milliseconds: 100)),

          const SizedBox(height: 12),

          // زر الطبقة التاريخية
          FloatingActionButton(
            heroTag: "historical_toggle",
            mini: true,
            backgroundColor:
                _showHistoricalLayer ? const Color(0xFFD4A574) : Colors.white,
            onPressed: _toggleHistoricalLayer,
            child: Icon(
              Iconsax.clock,
              color:
                  _showHistoricalLayer ? Colors.white : const Color(0xFFD4A574),
            ),
          ).animate().scale(delay: const Duration(milliseconds: 200)),

          const SizedBox(height: 12),

          // زر نوع الخريطة
          FloatingActionButton(
            heroTag: "map_type_toggle",
            mini: true,
            backgroundColor:
                _showSatellite ? const Color(0xFF2E8B57) : Colors.white,
            onPressed: _toggleMapType,
            child: Icon(
              _showSatellite ? Iconsax.map : Iconsax.global,
              color: _showSatellite ? Colors.white : const Color(0xFF2E8B57),
            ),
          ).animate().scale(delay: const Duration(milliseconds: 300)),
        ],
      ),
    );
  }

  Widget _buildZoomControls() {
    return Positioned(
      right: 16,
      bottom: 200,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            IconButton(
              onPressed: _zoomIn,
              icon: const Icon(
                Iconsax.add,
                color: Color(0xFF4F908E),
              ),
            ),
            Container(
              width: 30,
              height: 1,
              color: Colors.grey[300],
            ),
            IconButton(
              onPressed: _zoomOut,
              icon: const Icon(
                Iconsax.minus,
                color: Color(0xFF4F908E),
              ),
            ),
          ],
        ),
      ),
    ).animate().slideX(begin: 1, duration: const Duration(milliseconds: 400));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<PlacesProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Color(0xFF4F908E), Color(0xFF6BA3A0)],
                ),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 3,
                    ),
                    SizedBox(height: 20),
                    Text(
                      'جاري تحميل خريطة المدينة المنورة...',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          if (provider.error.isNotEmpty) {
            return Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Color(0xFFE74C3C), Color(0xFFC0392B)],
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Iconsax.warning_2,
                      color: Colors.white,
                      size: 64,
                    ),
                    const SizedBox(height: 20),
                    Text(
                      'حدث خطأ في تحميل الخريطة',
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      provider.error,
                      textAlign: TextAlign.center,
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                    const SizedBox(height: 20),
                    ElevatedButton.icon(
                      onPressed: () => provider.fetchPlaces(),
                      icon: const Icon(Iconsax.refresh),
                      label: Text(
                        'إعادة المحاولة',
                        style: GoogleFonts.ibmPlexSansArabic(),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white,
                        foregroundColor: const Color(0xFFE74C3C),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }

          return Stack(
            children: [
              // الخريطة الرئيسية
              GoogleMap(
                onMapCreated: _onMapCreated,
                initialCameraPosition: CameraPosition(
                  target: MapConstants.MADINAH_CENTER,
                  zoom: 14,
                  tilt: _show3D ? 45 : 0,
                ),
                markers: _markers,
                circles: _circles,
                polylines: _polylines,
                mapType: _showSatellite ? MapType.satellite : MapType.normal,
                myLocationEnabled: true,
                myLocationButtonEnabled: false,
                mapToolbarEnabled: false,
                zoomControlsEnabled: false,
                compassEnabled: true,
                buildingsEnabled: true,
                trafficEnabled: _showTraffic,
                minMaxZoomPreference: const MinMaxZoomPreference(10, 20),
              ),

              // شريط البحث
              Positioned(
                top: MediaQuery.of(context).padding.top + 10,
                left: 16,
                right: 80,
                child: _buildSearchBar(),
              ),

              // زر الرجوع
              Positioned(
                top: MediaQuery.of(context).padding.top + 16,
                right: 16,
                child: FloatingActionButton(
                  heroTag: "back_button",
                  mini: true,
                  backgroundColor: Colors.white,
                  onPressed: () => Navigator.pop(context),
                  child: const Icon(
                    Iconsax.arrow_right_3,
                    color: Color(0xFF4F908E),
                  ),
                ),
              ).animate().slideX(
                  begin: 1, duration: const Duration(milliseconds: 300)),

              // شريط الفئات
              Positioned(
                top: MediaQuery.of(context).padding.top + 80,
                left: 0,
                right: 0,
                child: _buildCategoriesSlider(),
              ),

              // أدوات التحكم العائمة
              _buildFloatingControls(),

              // أدوات التكبير
              _buildZoomControls(),

              // زر الموقع الحالي
              Positioned(
                left: 16,
                bottom: 100,
                child: FloatingActionButton(
                  heroTag: "current_location",
                  backgroundColor: const Color(0xFF4F908E),
                  onPressed: _goToCurrentLocation,
                  child: AnimatedBuilder(
                    animation: _pulseController,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: 1.0 + (_pulseController.value * 0.1),
                        child: const Icon(
                          Iconsax.location,
                          color: Colors.white,
                        ),
                      );
                    },
                  ),
                ),
              ).animate().scale(delay: const Duration(milliseconds: 500)),
            ],
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pulseController.dispose();
    _fabController.dispose();
    super.dispose();
  }
}

// نماذج البيانات
class PlaceCategory {
  final String id;
  final String name;
  final IconData icon;
  final Color color;
  final List<Color> gradient;

  PlaceCategory({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
    required this.gradient,
  });
}

class MapLayer {
  final String id;
  final String name;
  final IconData icon;
  final Color color;
  final bool isActive;

  MapLayer({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
    required this.isActive,
  });
}
