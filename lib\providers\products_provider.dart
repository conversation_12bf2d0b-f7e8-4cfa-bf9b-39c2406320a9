import 'package:flutter/foundation.dart';
import 'package:wiffada/models/product_model.dart';
import 'package:wiffada/services/products_service.dart';

class ProductsProvider with ChangeNotifier {
  final ProductsService _productsService;
  List<Product> _products = [];
  List<Product> _cartItems = [];
  List<Product> _favorites = [];
  bool _isLoading = false;
  String _error = '';
  String _selectedCategory = 'all';

  ProductsProvider({ProductsService? productsService})
      : _productsService = productsService ?? ProductsService();

  List<Product> get products => _products;
  List<Product> get cartItems => _cartItems;
  List<Product> get favorites => _favorites;
  bool get isLoading => _isLoading;
  String get error => _error;
  String get selectedCategory => _selectedCategory;
  
  int get cartItemCount => _cartItems.length;
  double get cartTotal => _cartItems.fold(0, (sum, item) => sum + item.price);

  Future<void> fetchProducts() async {
    _setLoading(true);
    try {
      _products = await _productsService.getProducts();
      _error = '';
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  Future<void> fetchProductsByCategory(String category) async {
    if (category == _selectedCategory && _products.isNotEmpty) return;
    
    _setLoading(true);
    _selectedCategory = category;
    
    try {
      _products = await _productsService.getProductsByCategory(category);
      _error = '';
    } catch (e) {
      _error = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  Future<Product?> getProductById(String id) async {
    try {
      return await _productsService.getProductById(id);
    } catch (e) {
      _error = e.toString();
      return null;
    }
  }

  void addToCart(Product product) {
    if (!_cartItems.contains(product)) {
      _cartItems.add(product);
      notifyListeners();
    }
  }

  void removeFromCart(Product product) {
    _cartItems.remove(product);
    notifyListeners();
  }

  void clearCart() {
    _cartItems.clear();
    notifyListeners();
  }

  void toggleFavorite(Product product) {
    if (_favorites.contains(product)) {
      _favorites.remove(product);
    } else {
      _favorites.add(product);
    }
    notifyListeners();
  }

  bool isFavorite(Product product) {
    return _favorites.contains(product);
  }

  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  List<String> getCategories() {
    return [
      'all',
      'dates',
      'perfumes',
      'souvenirs',
      'clothing',
      'food',
      'accessories',
    ];
  }
}
