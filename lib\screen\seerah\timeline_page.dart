import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax/iconsax.dart';

class TimelinePage extends StatefulWidget {
  const TimelinePage({super.key});

  @override
  State<TimelinePage> createState() => _TimelinePageState();
}

class _TimelinePageState extends State<TimelinePage> {
  int selectedEventIndex = 0;

  final List<Map<String, dynamic>> timelineEvents = [
    {
      'year': '571 م',
      'title': 'ولادة النبي ﷺ',
      'description': 'وُلد النبي محمد ﷺ في مكة المكرمة في عام الفيل',
      'details':
          'وُلد النبي محمد ﷺ في شهر ربيع الأول من عام الفيل، وكان يتيم الأب، وتوفيت أمه آمنة بنت وهب وهو في السادسة من عمره.',
      'icon': Iconsax.user,
      'color': const Color(0xFF3498DB),
      'image':
          'https://images.unsplash.com/photo-1564769625392-651b2c0e7b8b?w=400',
      'isInteractive': true,
    },
    {
      'year': '610 م',
      'title': 'بداية الوحي',
      'description': 'نزول الوحي في غار حراء',
      'details':
          'في شهر رمضان، نزل الوحي على النبي ﷺ في غار حراء بآيات "اقرأ باسم ربك الذي خلق"، وكان عمره 40 سنة.',
      'icon': Iconsax.book_1,
      'color': const Color(0xFF9B59B6),
      'image':
          'https://images.unsplash.com/photo-1542816417-0983c9c9ad53?w=400',
      'isInteractive': true,
    },
    {
      'year': '613 م',
      'title': 'الدعوة الجهرية',
      'description': 'بداية الدعوة العلنية للإسلام',
      'details':
          'بدأ النبي ﷺ بالدعوة العلنية بعد ثلاث سنوات من الدعوة السرية، وواجه معارضة شديدة من قريش.',
      'icon': Iconsax.microphone,
      'color': const Color(0xFFE74C3C),
      'image':
          'https://images.unsplash.com/photo-1591604129939-f1efa4d9f7fa?w=400',
      'isInteractive': false,
    },
    {
      'year': '622 م',
      'title': 'الهجرة إلى المدينة',
      'description': 'الهجرة النبوية من مكة إلى المدينة المنورة',
      'details':
          'هاجر النبي ﷺ مع أبي بكر الصديق من مكة إلى المدينة المنورة، وهو الحدث الذي بدأ به التاريخ الهجري.',
      'icon': Iconsax.location,
      'color': const Color(0xFF27AE60),
      'image':
          'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
      'isInteractive': true,
    },
    {
      'year': '629 م',
      'title': 'صلح الحديبية',
      'description': 'معاهدة السلام مع قريش',
      'details':
          'وقع النبي ﷺ صلح الحديبية مع قريش، والذي كان فتحاً مبيناً رغم ظاهره كهدنة.',
      'icon': Iconsax.people,
      'color': const Color(0xFFF39C12),
      'image':
          'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400',
      'isInteractive': false,
    },
    {
      'year': '630 م',
      'title': 'فتح مكة',
      'description': 'الفتح المبين لمكة المكرمة',
      'details':
          'فتح النبي ﷺ مكة المكرمة فتحاً مبيناً، وأعلن العفو العام وطهر الكعبة من الأصنام.',
      'icon': Iconsax.flag,
      'color': const Color(0xFF8E44AD),
      'image':
          'https://images.unsplash.com/photo-1591604129939-f1efa4d9f7fa?w=400',
      'isInteractive': true,
    },
    {
      'year': '632 م',
      'title': 'حجة الوداع',
      'description': 'آخر حج للنبي ﷺ',
      'details':
          'أدى النبي ﷺ حجة الوداع وألقى خطبته الشهيرة، وتوفي بعدها بثلاثة أشهر.',
      'icon': Iconsax.home_2,
      'color': const Color(0xFF34495E),
      'image':
          'https://images.unsplash.com/photo-1591604129939-f1efa4d9f7fa?w=400',
      'isInteractive': true,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: const Color(0xFF4F908E),
        elevation: 0,
        title: Text(
          'الخط الزمني التفاعلي',
          style: GoogleFonts.ibmPlexSansArabic(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          _buildTimelineHeader(),
          Expanded(
            child: Row(
              children: [
                _buildTimelineList(),
                _buildEventDetails(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimelineHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF4F908E),
            const Color(0xFF4F908E).withOpacity(0.8),
          ],
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Iconsax.calendar,
            size: 48,
            color: Colors.white,
          ).animate().scale(duration: const Duration(milliseconds: 600)),
          const SizedBox(height: 12),
          Text(
            'رحلة حياة النبي محمد ﷺ',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ).animate().fadeIn(delay: const Duration(milliseconds: 200)),
          const SizedBox(height: 8),
          Text(
            'اكتشف الأحداث المهمة في حياة خير البشر',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              color: Colors.white.withOpacity(0.9),
            ),
          ).animate().fadeIn(delay: const Duration(milliseconds: 400)),
        ],
      ),
    );
  }

  Widget _buildTimelineList() {
    return Container(
      width: MediaQuery.of(context).size.width * 0.4,
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          right: BorderSide(color: Color(0xFFE0E0E0), width: 1),
        ),
      ),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: timelineEvents.length,
        itemBuilder: (context, index) {
          return _buildTimelineItem(index);
        },
      ),
    );
  }

  Widget _buildTimelineItem(int index) {
    final event = timelineEvents[index];
    final isSelected = index == selectedEventIndex;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedEventIndex = index;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        child: Row(
          children: [
            // Timeline line and dot
            Column(
              children: [
                if (index > 0)
                  Container(
                    width: 2,
                    height: 20,
                    color: const Color(0xFF4F908E).withOpacity(0.3),
                  ),
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color:
                        isSelected ? const Color(0xFF4F908E) : Colors.grey[300],
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF4F908E)
                          : Colors.grey[400]!,
                      width: 2,
                    ),
                  ),
                ),
                if (index < timelineEvents.length - 1)
                  Container(
                    width: 2,
                    height: 40,
                    color: const Color(0xFF4F908E).withOpacity(0.3),
                  ),
              ],
            ),
            const SizedBox(width: 16),
            // Event info
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color(0xFF4F908E).withOpacity(0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isSelected
                        ? const Color(0xFF4F908E)
                        : Colors.transparent,
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          event['icon'],
                          size: 16,
                          color: event['color'],
                        ),
                        const SizedBox(width: 8),
                        Text(
                          event['year'],
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF4F908E),
                          ),
                        ),
                        if (event['isInteractive'])
                          Container(
                            margin: const EdgeInsets.only(right: 8),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: const Color(0xFFE74C3C),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              'تفاعلي',
                              style: GoogleFonts.ibmPlexSansArabic(
                                fontSize: 8,
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      event['title'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: const Color(0xFF2C3E50),
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      event['description'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    )
        .animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn(duration: const Duration(milliseconds: 600))
        .slideX(begin: -0.3, end: 0);
  }

  Widget _buildEventDetails() {
    final event = timelineEvents[selectedEventIndex];

    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Event header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: (event['color'] as Color).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Icon(
                    event['icon'],
                    size: 32,
                    color: event['color'],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        event['year'],
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF4F908E),
                        ),
                      ),
                      Text(
                        event['title'],
                        style: GoogleFonts.ibmPlexSansArabic(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF2C3E50),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Event image
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                image: DecorationImage(
                  image: NetworkImage(event['image']),
                  fit: BoxFit.cover,
                ),
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withOpacity(0.3),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Event details
            Text(
              'تفاصيل الحدث',
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2C3E50),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              event['details'],
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 16,
                color: Colors.grey[700],
                height: 1.6,
              ),
            ),
            const SizedBox(height: 24),

            // Interactive button
            if (event['isInteractive'])
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    _showInteractiveDialog(event);
                  },
                  icon: const Icon(Iconsax.play_circle, color: Colors.white),
                  label: Text(
                    'تجربة تفاعلية',
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4F908E),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showInteractiveDialog(Map<String, dynamic> event) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(event['icon'], color: event['color']),
            const SizedBox(width: 8),
            Text(
              'تجربة تفاعلية',
              style: GoogleFonts.ibmPlexSansArabic(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'ستتمكن قريباً من عيش هذا الحدث بتقنية الواقع الافتراضي والتفاعل مع الشخصيات التاريخية!',
              style: GoogleFonts.ibmPlexSansArabic(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Icon(
              Iconsax.video_play,
              size: 64,
              color: event['color'],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'حسناً',
              style: GoogleFonts.ibmPlexSansArabic(
                color: const Color(0xFF4F908E),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
