import 'dart:developer';

class WebRTCService {
  // Placeholder properties for compatibility
  Function(dynamic)? onRemoteStream;

  Future<void> initialize() async {
    // TODO: Implement WebRTC when flutter_webrtc is added back
    log('WebRTC service initialized (placeholder)');
  }

  Future<void> connect() async {
    // TODO: Implement WebRTC connection when flutter_webrtc is added back
    log('WebRTC connection (placeholder)');
  }

  void dispose() {
    // TODO: Implement disposal when flutter_webrtc is added back
    log('WebRTC service disposed (placeholder)');
  }
}
