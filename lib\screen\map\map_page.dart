import 'dart:async';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:wiffada/utils/map_constants.dart';
import 'package:wiffada/models/place.dart';
import 'package:wiffada/providers/places_provider.dart';
import 'package:wiffada/screen/map/place_details_page.dart';
import 'package:wiffada/theme/app_colors.dart';
import 'package:flutter_animate/flutter_animate.dart';

class MapPage extends StatefulWidget {
  final String? initialFilter;

  const MapPage({super.key, this.initialFilter});

  @override
  State<MapPage> createState() => _MapPageState();
}

class _MapPageState extends State<MapPage> with SingleTickerProviderStateMixin {
  final Completer<GoogleMapController> _mapController = Completer();
  late AnimationController _animationController;
  Set<Marker> _markers = {};
  String _searchQuery = '';
  String? currentFilter;

  final List<Category> _categories = [
    Category(
      id: 'mosques',
      name: 'المساجد',
      icon: Icons.mosque,
      color: Colors.green,
    ),
    Category(
      id: 'mountains',
      name: 'الجبال',
      icon: Icons.landscape,
      color: Colors.brown,
    ),
    Category(
      id: 'historical',
      name: 'المعالم التاريخية',
      icon: Icons.history_edu,
      color: Colors.orange,
    ),
    Category(
      id: 'markets',
      name: 'الأسواق',
      icon: Icons.store,
      color: Colors.blue,
    ),
    Category(
      id: 'museums',
      name: 'المتاحف',
      icon: Icons.museum,
      color: Colors.purple,
    ),
  ];

  String _selectedCategory = 'mosques';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    currentFilter = widget.initialFilter;
    if (currentFilter != null) {
      _applyFilter(currentFilter!);
    }
    // استخدام تأخير بسيط لتحميل البيانات بعد بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadPlaces();
    });
  }

  Future<void> _loadPlaces() async {
    final provider = Provider.of<PlacesProvider>(context, listen: false);
    await provider.fetchPlaces();
    _updateMarkers(provider.places);
  }

  Future<void> _updateMarkers(List<Place> places) async {
    final markers = <Marker>{};
    for (final place in places) {
      if (_selectedCategory.isEmpty || place.category == _selectedCategory) {
        final icon = await _getMarkerIcon(place.category);
        markers.add(
          Marker(
            markerId: MarkerId(place.id),
            position: place.location,
            icon: icon,
            onTap: () => _onMarkerTapped(place),
          ),
        );
      }
    }
    setState(() {
      _markers = markers;
    });
  }

  Future<BitmapDescriptor> _getMarkerIcon(String category) async {
    // تحديد الأيقونة واللون حسب الفئة
    IconData iconData;
    Color color;

    switch (category) {
      case 'mosques':
        iconData = Icons.mosque;
        color = Colors.green;
        break;
      case 'mountains':
        iconData = Icons.landscape;
        color = Colors.brown;
        break;
      case 'historical':
        iconData = Icons.history_edu;
        color = Colors.orange;
        break;
      case 'markets':
        iconData = Icons.store;
        color = Colors.blue;
        break;
      case 'museums':
        iconData = Icons.museum;
        color = Colors.purple;
        break;
      default:
        iconData = Icons.place;
        color = Colors.grey;
    }

    // إنشاء صورة الأيقونة
    final pictureRecorder = PictureRecorder();
    final canvas = Canvas(pictureRecorder);
    const size = Size(48, 48);

    // رسم الدائرة الخلفية
    final Paint circlePaint = Paint()
      ..color = color.withOpacity(0.9)
      ..style = PaintingStyle.fill;
    canvas.drawCircle(size.center(Offset.zero), 20, circlePaint);

    // رسم الحدود
    final Paint borderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    canvas.drawCircle(size.center(Offset.zero), 20, borderPaint);

    // رسم الأيقونة
    TextPainter textPainter = TextPainter(textDirection: TextDirection.rtl);
    textPainter.text = TextSpan(
      text: String.fromCharCode(iconData.codePoint),
      style: TextStyle(
        fontSize: 24,
        fontFamily: iconData.fontFamily,
        color: Colors.white,
      ),
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      size.center(Offset(-textPainter.width / 2, -textPainter.height / 2)),
    );

    // تحويل الرسم إلى صورة
    final picture = pictureRecorder.endRecording();
    final image = await picture.toImage(size.width.toInt(), size.height.toInt());
    final bytes = await image.toByteData(format: ImageByteFormat.png);

    // ignore: deprecated_member_use
    return BitmapDescriptor.fromBytes(bytes!.buffer.asUint8List());
  }


  void _onMapCreated(GoogleMapController controller) {
    _mapController.complete(controller);
    // تخصيص نمط الخريطة

    // ignore: deprecated_member_use
    controller.setMapStyle('''
      [
        {
          "elementType": "geometry",
          "stylers": [
            {
              "color": "#f0f4f5"
            }
          ]
        },
        {
          "elementType": "labels.icon",
          "stylers": [
            {
              "visibility": "off"
            }
          ]
        },
        {
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#4F908E"
            }
          ]
        },
        {
          "elementType": "labels.text.stroke",
          "stylers": [
            {
              "color": "#ffffff"
            }
          ]
        },
        {
          "featureType": "administrative",
          "elementType": "geometry.stroke",
          "stylers": [
            {
              "color": "#b3cfd3"
            }
          ]
        },
        {
          "featureType": "administrative.land_parcel",
          "elementType": "geometry.stroke",
          "stylers": [
            {
              "color": "#dce2e3"
            }
          ]
        },
        {
          "featureType": "administrative.land_parcel",
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#4F908E"
            }
          ]
        },
        {
          "featureType": "administrative.locality",
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#4F908E"
            },
            {
              "weight": 1
            }
          ]
        },
        {
          "featureType": "administrative.neighborhood",
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#4F908E"
            }
          ]
        },
        {
          "featureType": "administrative.province",
          "elementType": "geometry.stroke",
          "stylers": [
            {
              "color": "#8abdc1"
            }
          ]
        },
        {
          "featureType": "landscape",
          "elementType": "geometry",
          "stylers": [
            {
              "color": "#e8f0f0"
            }
          ]
        },
        {
          "featureType": "landscape.man_made",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "color": "#e8f0f0"
            }
          ]
        },
        {
          "featureType": "landscape.natural",
          "elementType": "geometry",
          "stylers": [
            {
              "color": "#dce8e7"
            }
          ]
        },
        {
          "featureType": "landscape.natural.landcover",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "color": "#dce8e7"
            }
          ]
        },
        {
          "featureType": "landscape.natural.terrain",
          "elementType": "geometry",
          "stylers": [
            {
              "color": "#c5d6d3"
            }
          ]
        },
        {
          "featureType": "poi",
          "elementType": "geometry",
          "stylers": [
            {
              "color": "#d3e5e3"
            }
          ]
        },
        {
          "featureType": "poi",
          "elementType": "labels.text",
          "stylers": [
            {
              "visibility": "simplified"
            }
          ]
        },
        {
          "featureType": "poi",
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#4F908E"
            }
          ]
        },
        {
          "featureType": "poi.attraction",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "color": "#c1ded9"
            }
          ]
        },
        {
          "featureType": "poi.business",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "color": "#d3e5e3"
            }
          ]
        },
        {
          "featureType": "poi.government",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "color": "#c1ded9"
            }
          ]
        },
        {
          "featureType": "poi.medical",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "color": "#c1ded9"
            }
          ]
        },
        {
          "featureType": "poi.park",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "color": "#b0d5c8"
            }
          ]
        },
        {
          "featureType": "poi.place_of_worship",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "color": "#c1ded9"
            }
          ]
        },
        {
          "featureType": "poi.school",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "color": "#c1ded9"
            }
          ]
        },
        {
          "featureType": "poi.sports_complex",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "color": "#c1ded9"
            }
          ]
        },
        {
          "featureType": "road",
          "elementType": "geometry",
          "stylers": [
            {
              "color": "#ffffff"
            }
          ]
        },
        {
          "featureType": "road",
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#7b9996"
            }
          ]
        },
        {
          "featureType": "road.arterial",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "color": "#e3eded"
            }
          ]
        },
        {
          "featureType": "road.highway",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "color": "#d3e5e3"
            }
          ]
        },
        {
          "featureType": "road.highway",
          "elementType": "geometry.stroke",
          "stylers": [
            {
              "color": "#b3cfd3"
            }
          ]
        },
        {
          "featureType": "road.local",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "color": "#f0f4f5"
            }
          ]
        },
        {
          "featureType": "transit.line",
          "elementType": "geometry",
          "stylers": [
            {
              "color": "#b3cfd3"
            }
          ]
        },
        {
          "featureType": "transit.station",
          "elementType": "geometry",
          "stylers": [
            {
              "color": "#d3e5e3"
            }
          ]
        },
        {
          "featureType": "water",
          "elementType": "geometry.fill",
          "stylers": [
            {
              "color": "#8fd0e8"
            }
          ]
        },
        {
          "featureType": "water",
          "elementType": "labels.text.fill",
          "stylers": [
            {
              "color": "#4F908E"
            }
          ]
        }
      ]
    ''');
  }

  Future<void> _goToCurrentLocation() async {
    final controller = await _mapController.future;
    controller.animateCamera(
      CameraUpdate.newLatLng(MapConstants.MADINAH_CENTER),
    );
  }

  Future<void> _zoomIn() async {
    final controller = await _mapController.future;
    controller.animateCamera(CameraUpdate.zoomIn());
  }

  Future<void> _zoomOut() async {
    final controller = await _mapController.future;
    controller.animateCamera(CameraUpdate.zoomOut());
  }


    void _onMarkerTapped(Place place) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => PlaceDetailsPage(
          place: place,
        ),
      ),
    );
  }



  Widget _buildCategoriesList() {
    return Container(
      height: 60,
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = category.id == _selectedCategory;
          return Padding(
            padding: const EdgeInsets.only(right: 12),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  setState(() {
                    _selectedCategory = category.id;
                  });
                  final provider = Provider.of<PlacesProvider>(context, listen: false);
                  _updateMarkers(provider.places);
                },
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected ? category.color : Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: isSelected ? [
                      BoxShadow(
                        color: category.color.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ] : null,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        category.icon,
                        color: isSelected ? Colors.white : category.color,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        category.name,
                        style: GoogleFonts.ibmPlexSansArabic(
                          color: isSelected ? Colors.white : Colors.black87,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ).animate().fadeIn().slideX(
                begin: 0.1,
                delay: Duration(milliseconds: index * 50),
                duration: const Duration(milliseconds: 300),
              );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<PlacesProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(
                color: AppColors.primary,
                strokeWidth: 3,
              ),
            );
          }

          if (provider.error.isNotEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: 48,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'حدث خطأ: ${provider.error}',
                    textAlign: TextAlign.center,
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 16,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
            );
          }

          return Stack(
            children: [
              GoogleMap(
                onMapCreated: _onMapCreated,
                initialCameraPosition: const CameraPosition(
                  target: MapConstants.MADINAH_CENTER,
                  zoom: MapConstants.DEFAULT_ZOOM,
                ),
                markers: _markers,
                minMaxZoomPreference: const MinMaxZoomPreference(
                  MapConstants.MIN_ZOOM,
                  MapConstants.MAX_ZOOM,
                ),
                myLocationEnabled: true,
                myLocationButtonEnabled: false,
                mapToolbarEnabled: false,
                zoomControlsEnabled: false,
                compassEnabled: false,
                buildingsEnabled: true,
                mapType: MapType.normal,

              ),

              Positioned(
                top: MediaQuery.of(context).padding.top + 16,
                right: 16,
                child: Container(
                  height: 56,
                  width: 110,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.06),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                        spreadRadius: 0,
                      ),
                      BoxShadow(
                        color: AppColors.primary.withOpacity(0.03),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ],
                    border: Border.all(
                      color: AppColors.primary.withOpacity(0.08),
                      width: 1.5,
                    ),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => Navigator.of(context).pop(),
                      borderRadius: BorderRadius.circular(16),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: const Icon(
                                Icons.arrow_back_ios_new,
                                color: AppColors.primary,
                                size: 16,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'رجوع',
                              style: GoogleFonts.ibmPlexSansArabic(
                                color: AppColors.primary,
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ).animate().fadeIn().slideX(begin: 0.3, duration: const Duration(milliseconds: 500)),

              Positioned(
                top: MediaQuery.of(context).padding.top + 16,
                left: 16,
                right: 132, // مسافة كافية لزر الرجوع
                child: Container(
                  height: 56,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.06),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                        spreadRadius: 0,
                      ),
                      BoxShadow(
                        color: AppColors.primary.withOpacity(0.03),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ],
                    border: Border.all(
                      color: AppColors.primary.withOpacity(0.08),
                      width: 1.5,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        margin: const EdgeInsets.only(right: 8, left: 16),
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.search,
                          color: AppColors.primary,
                          size: 20,
                        ),
                      ),
                      Expanded(
                        child: TextField(
                          onChanged: (value) {
                            setState(() {
                              _searchQuery = value;
                            });
                          },
                          decoration: InputDecoration(
                            hintText: 'ابحث عن مكان في المدينة المنورة',
                            hintStyle: GoogleFonts.ibmPlexSansArabic(
                              color: Colors.grey[400],
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                            border: InputBorder.none,
                            contentPadding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          style: GoogleFonts.ibmPlexSansArabic(
                            color: Colors.black87,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      if (_searchQuery.isNotEmpty)
                        Container(
                          margin: const EdgeInsets.only(left: 8, right: 8),
                          decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Material(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(12),
                            child: InkWell(
                              onTap: () {
                                setState(() {
                                  _searchQuery = '';
                                });
                              },
                              borderRadius: BorderRadius.circular(12),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Icon(
                                  Icons.close,
                                  size: 18,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ).animate().fadeIn().slideY(begin: -0.3, duration: const Duration(milliseconds: 500)),
              Positioned(
                top: MediaQuery.of(context).padding.top + 88,
                left: 0,
                right: 0,
                height: 50,
                child: _buildCategoriesList(),
              ),

              Positioned(
                right: 16,
                bottom: 24,
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.08),
                            blurRadius: 20,
                            offset: const Offset(0, 4),
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Column(
                        children: [
                          Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: _zoomIn,
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(12),
                                topRight: Radius.circular(12),
                              ),
                              child: Container(
                                height: 44,
                                width: 44,
                                alignment: Alignment.center,
                                child: const Icon(
                                  Icons.add,
                                  color: AppColors.primary,
                                  size: 24,
                                ),
                              ),
                            ),
                          ),
                          Container(
                            width: 24,
                            height: 1,
                            color: Colors.grey[200],
                          ),
                          Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: _zoomOut,
                              borderRadius: const BorderRadius.only(
                                bottomLeft: Radius.circular(12),
                                bottomRight: Radius.circular(12),
                              ),
                              child: Container(
                                height: 44,
                                width: 44,
                                alignment: Alignment.center,
                                child: const Icon(
                                  Icons.remove,
                                  color: AppColors.primary,
                                  size: 24,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ).animate().fadeIn().slideX(duration: const Duration(milliseconds: 300)),
                    const SizedBox(height: 16),
                    Container(
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withOpacity(0.3),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                        child: InkWell(
                          onTap: _goToCurrentLocation,
                          borderRadius: BorderRadius.circular(12),
                          child: Container(
                            height: 44,
                            width: 44,
                            alignment: Alignment.center,
                            child: const Icon(
                              Icons.my_location,
                              color: Colors.white,
                              size: 22,
                            ),
                          ),
                        ),
                      ),
                    ).animate().fadeIn().scale(delay: const Duration(milliseconds: 200)),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _applyFilter(String filter) {
    setState(() {
      currentFilter = filter;
      // هنا يمكن تنفيذ منطق تصفية الخريطة بناءً على النوع
    });
  }

  @override
  /// Disposes of the animation controller and calls the superclass [dispose].
  ///
  /// This method should be called when the widget is no longer needed to free up resources.
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}

class Category {
  final String id;
  final String name;
  final IconData icon;
  final Color color;

  Category({
    required this.id,
    required this.name,
    required this.icon,
    required this.color,
  });
}
