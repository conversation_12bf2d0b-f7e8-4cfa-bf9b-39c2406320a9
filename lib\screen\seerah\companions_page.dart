import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax/iconsax.dart';

class CompanionsPage extends StatefulWidget {
  const CompanionsPage({super.key});

  @override
  State<CompanionsPage> createState() => _CompanionsPageState();
}

class _CompanionsPageState extends State<CompanionsPage> {
  String selectedCategory = 'الخلفاء الراشدون';
  int selectedCompanionIndex = 0;

  final Map<String, List<Map<String, dynamic>>> companionsData = {
    'الخلفاء الراشدون': [
      {
        'name': 'أبو بكر الصديق',
        'title': 'الخليفة الأول',
        'description': 'أول من آمن من الرجال، وأول خليفة للمسلمين',
        'fullDescription':
            'عبد الله بن أبي قحافة، أول من آمن برسالة النبي ﷺ من الرجال، وأقرب أصحابه إليه. كان تاجراً ثرياً وأنفق ماله في سبيل الله. صاحب النبي في الهجرة وأول خليفة للمسلمين بعد وفاة النبي ﷺ.',
        'qualities': ['الصدق', 'الكرم', 'الشجاعة', 'التواضع'],
        'famousStory':
            'عندما أُسري بالنبي ﷺ ليلاً، صدق أبو بكر الخبر دون تردد، فلُقب بالصديق',
        'icon': Iconsax.crown,
        'color': const Color(0xFF3498DB),
        'avatar':
            'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      },
      {
        'name': 'عمر بن الخطاب',
        'title': 'الفاروق',
        'description':
            'ثاني الخلفاء الراشدين، الذي فرق الله به بين الحق والباطل',
        'fullDescription':
            'عمر بن الخطاب الفاروق، ثاني الخلفاء الراشدين. كان قوياً شجاعاً، أعز الله به الإسلام. اشتهر بعدله وحزمه في الحكم، ووسع رقعة الدولة الإسلامية.',
        'qualities': ['العدل', 'القوة', 'الحزم', 'التقوى'],
        'famousStory':
            'أسلم بعد أن سمع القرآن من أخته فاطمة، وخرج معلناً إسلامه جهراً',
        'icon': Iconsax.shield_tick,
        'color': const Color(0xFFE74C3C),
        'avatar':
            'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      },
      {
        'name': 'عثمان بن عفان',
        'title': 'ذو النورين',
        'description': 'ثالث الخلفاء الراشدين، تزوج ابنتي النبي ﷺ',
        'fullDescription':
            'عثمان بن عفان ذو النورين، لأنه تزوج ابنتي النبي ﷺ رقية ثم أم كلثوم. كان تاجراً ثرياً وأنفق ماله في سبيل الله، وجمع القرآن في مصحف واحد.',
        'qualities': ['الكرم', 'الحياء', 'التقوى', 'الصبر'],
        'famousStory':
            'جهز جيش العسرة بماله، فقال النبي ﷺ: "ما ضر عثمان ما عمل بعد اليوم"',
        'icon': Iconsax.book_1,
        'color': const Color(0xFF9B59B6),
        'avatar':
            'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150',
      },
      {
        'name': 'علي بن أبي طالب',
        'title': 'أبو الحسن',
        'description': 'رابع الخلفاء الراشدين، ابن عم النبي ﷺ وزوج فاطمة',
        'fullDescription':
            'علي بن أبي طالب، ابن عم النبي ﷺ وزوج ابنته فاطمة. أول من أسلم من الصبيان، وكان شجاعاً مقداماً وعالماً فقيهاً.',
        'qualities': ['الشجاعة', 'العلم', 'الفقه', 'الزهد'],
        'famousStory':
            'نام في فراش النبي ﷺ ليلة الهجرة، مضحياً بنفسه لحماية النبي',
        'icon': Iconsax.shield_tick,
        'color': const Color(0xFF27AE60),
        'avatar':
            'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150',
      },
    ],
    'الصحابيات': [
      {
        'name': 'خديجة بنت خويلد',
        'title': 'أم المؤمنين الأولى',
        'description': 'زوجة النبي ﷺ الأولى، أول من آمن من النساء',
        'fullDescription':
            'خديجة بنت خويلد، زوجة النبي ﷺ الأولى وأم أولاده. كانت تاجرة ثرية، آمنت به وساندته في بداية الدعوة.',
        'qualities': ['الإيمان', 'المساندة', 'الكرم', 'الحكمة'],
        'famousStory': 'أول من آمن برسالة النبي ﷺ وساندته في أصعب الأوقات',
        'icon': Iconsax.heart,
        'color': const Color(0xFFE91E63),
        'avatar':
            'https://images.unsplash.com/photo-1494790108755-2616c6d4e6e8?w=150',
      },
      {
        'name': 'عائشة بنت أبي بكر',
        'title': 'أم المؤمنين',
        'description': 'زوجة النبي ﷺ، عالمة فقيهة ومحدثة',
        'fullDescription':
            'عائشة بنت أبي بكر الصديق، زوجة النبي ﷺ وأحب نسائه إليه. كانت عالمة فقيهة، روت الكثير من الأحاديث.',
        'qualities': ['العلم', 'الفقه', 'الذكاء', 'التقوى'],
        'famousStory': 'كانت أحب زوجات النبي ﷺ إليه، وتوفي وهو في حجرها',
        'icon': Iconsax.teacher,
        'color': const Color(0xFFF39C12),
        'avatar':
            'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      },
    ],
    'العشرة المبشرون': [
      {
        'name': 'طلحة بن عبيد الله',
        'title': 'طلحة الخير',
        'description': 'أحد العشرة المبشرين بالجنة، اشتهر بكرمه',
        'fullDescription':
            'طلحة بن عبيد الله، أحد العشرة المبشرين بالجنة. كان تاجراً ثرياً وكريماً، لُقب بطلحة الخير لكثرة إنفاقه.',
        'qualities': ['الكرم', 'الشجاعة', 'التجارة', 'الجود'],
        'famousStory': 'أنفق كل ماله في يوم واحد على الفقراء والمحتاجين',
        'icon': Iconsax.coin,
        'color': const Color(0xFF16A085),
        'avatar':
            'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      },
      {
        'name': 'الزبير بن العوام',
        'title': 'حواري رسول الله',
        'description': 'ابن عمة النبي ﷺ، أحد العشرة المبشرين بالجنة',
        'fullDescription':
            'الزبير بن العوام، ابن عمة النبي ﷺ صفية بنت عبد المطلب. كان شجاعاً مقداماً، لُقب بحواري رسول الله.',
        'qualities': ['الشجاعة', 'الإقدام', 'الوفاء', 'النسب'],
        'famousStory': 'أول من سل سيفاً في سبيل الله في الإسلام',
        'icon': Iconsax.shield,
        'color': const Color(0xFF8E44AD),
        'avatar':
            'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      },
    ],
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        backgroundColor: const Color(0xFF4F908E),
        elevation: 0,
        title: Text(
          'معرض الصحابة',
          style: GoogleFonts.ibmPlexSansArabic(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Column(
        children: [
          _buildHeader(),
          _buildCategoryTabs(),
          Expanded(
            child: Row(
              children: [
                _buildCompanionsList(),
                _buildCompanionDetails(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            const Color(0xFF4F908E),
            const Color(0xFF4F908E).withOpacity(0.8),
          ],
        ),
      ),
      child: Column(
        children: [
          const Icon(
            Iconsax.people,
            size: 48,
            color: Colors.white,
          ).animate().scale(duration: const Duration(milliseconds: 600)),
          const SizedBox(height: 12),
          Text(
            'أصحاب النبي محمد ﷺ',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ).animate().fadeIn(delay: const Duration(milliseconds: 200)),
          const SizedBox(height: 8),
          Text(
            'تعرف على سير وقصص أصحاب النبي الكرام',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              color: Colors.white.withOpacity(0.9),
            ),
          ).animate().fadeIn(delay: const Duration(milliseconds: 400)),
        ],
      ),
    );
  }

  Widget _buildCategoryTabs() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: companionsData.keys.length,
        itemBuilder: (context, index) {
          final category = companionsData.keys.elementAt(index);
          final isSelected = category == selectedCategory;

          return GestureDetector(
            onTap: () {
              setState(() {
                selectedCategory = category;
                selectedCompanionIndex = 0;
              });
            },
            child: Container(
              margin: const EdgeInsets.only(left: 12),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              decoration: BoxDecoration(
                color: isSelected ? const Color(0xFF4F908E) : Colors.white,
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color:
                      isSelected ? const Color(0xFF4F908E) : Colors.grey[300]!,
                ),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: const Color(0xFF4F908E).withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ]
                    : null,
              ),
              child: Center(
                child: Text(
                  category,
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: isSelected ? Colors.white : Colors.grey[700],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCompanionsList() {
    final companions = companionsData[selectedCategory] ?? [];

    return Container(
      width: MediaQuery.of(context).size.width * 0.35,
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(color: Color(0xFFE0E0E0), width: 1),
        ),
      ),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: companions.length,
        itemBuilder: (context, index) {
          return _buildCompanionCard(companions[index], index);
        },
      ),
    );
  }

  Widget _buildCompanionCard(Map<String, dynamic> companion, int index) {
    final isSelected = index == selectedCompanionIndex;

    return GestureDetector(
      onTap: () {
        setState(() {
          selectedCompanionIndex = index;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF4F908E).withOpacity(0.1)
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? const Color(0xFF4F908E) : Colors.transparent,
            width: 2,
          ),
        ),
        child: Column(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundImage: NetworkImage(companion['avatar']),
              backgroundColor: (companion['color'] as Color).withOpacity(0.2),
            ),
            const SizedBox(height: 12),
            Text(
              companion['name'],
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2C3E50),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              companion['title'],
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: 12,
                color: companion['color'],
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    )
        .animate(delay: Duration(milliseconds: 100 * index))
        .fadeIn(duration: const Duration(milliseconds: 600))
        .slideY(begin: 0.3, end: 0);
  }

  Widget _buildCompanionDetails() {
    final companions = companionsData[selectedCategory] ?? [];
    if (companions.isEmpty) return const SizedBox();

    final companion = companions[selectedCompanionIndex];

    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(24),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundImage: NetworkImage(companion['avatar']),
                    backgroundColor:
                        (companion['color'] as Color).withOpacity(0.2),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          companion['name'],
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: const Color(0xFF2C3E50),
                          ),
                        ),
                        Text(
                          companion['title'],
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 16,
                            color: companion['color'],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    companion['icon'],
                    size: 32,
                    color: companion['color'],
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Description
              Text(
                'نبذة عن الصحابي',
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2C3E50),
                ),
              ),
              const SizedBox(height: 12),
              Text(
                companion['fullDescription'],
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 16,
                  color: Colors.grey[700],
                  height: 1.6,
                ),
              ),
              const SizedBox(height: 24),

              // Qualities
              Text(
                'الصفات المميزة',
                style: GoogleFonts.ibmPlexSansArabic(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF2C3E50),
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children:
                    (companion['qualities'] as List<String>).map((quality) {
                  return Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: (companion['color'] as Color).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: (companion['color'] as Color).withOpacity(0.3),
                      ),
                    ),
                    child: Text(
                      quality,
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 14,
                        color: companion['color'],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 24),

              // Famous story
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: (companion['color'] as Color).withOpacity(0.05),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: (companion['color'] as Color).withOpacity(0.2),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Iconsax.story,
                          color: companion['color'],
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'قصة مشهورة',
                          style: GoogleFonts.ibmPlexSansArabic(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: companion['color'],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      companion['famousStory'],
                      style: GoogleFonts.ibmPlexSansArabic(
                        fontSize: 15,
                        color: Colors.grey[700],
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Interactive button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: () {
                    _showCompanionDialog(companion);
                  },
                  icon: const Icon(Iconsax.video_play, color: Colors.white),
                  label: Text(
                    'تجربة تفاعلية',
                    style: GoogleFonts.ibmPlexSansArabic(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: companion['color'],
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCompanionDialog(Map<String, dynamic> companion) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: Row(
          children: [
            Icon(companion['icon'], color: companion['color']),
            const SizedBox(width: 8),
            Text(
              'تجربة تفاعلية',
              style: GoogleFonts.ibmPlexSansArabic(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircleAvatar(
              radius: 40,
              backgroundImage: NetworkImage(companion['avatar']),
            ),
            const SizedBox(height: 16),
            Text(
              'ستتمكن قريباً من التفاعل مع ${companion['name']} والتعلم من قصصه وحكمه!',
              style: GoogleFonts.ibmPlexSansArabic(fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'حسناً',
              style: GoogleFonts.ibmPlexSansArabic(
                color: const Color(0xFF4F908E),
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
