import 'package:wiffada/models/product_model.dart';

class ProductsService {
  Future<List<Product>> getProducts() async {
    try {
      // في المستقبل، يمكن استبدال هذا بطلب API حقيقي
      return _getMockProducts();
    } catch (e) {
      throw Exception('Failed to load products');
    }
  }

  Future<List<Product>> getProductsByCategory(String category) async {
    try {
      final products = await _getMockProducts();
      if (category == 'all') return products;

      // تصفية المنتجات حسب الفئة
      return products.where((product) => product.category == category).toList();
    } catch (e) {
      throw Exception('فشل في تحميل المنتجات حسب التصنيف');
    }
  }

  Future<Product?> getProductById(String id) async {
    try {
      final products = await _getMockProducts();
      return products.firstWhere((product) => product.id == id);
    } catch (e) {
      throw Exception('فشل في العثور على المنتج');
    }
  }

  List<Product> _getMockProducts() {
    return [
      ..._getDates(),
      ..._getPerfumes(),
      ..._getSouvenirs(),
      ..._getClothing(),
      ..._getFood(),
      ..._getAccessories(),
    ];
  }

  List<Product> _getDates() {
    return [
      Product(
        id: 'date-1',
        name: 'تمر عجوة المدينة',
        description: 'تمر عجوة المدينة المنورة الأصلي، معروف بفوائده الصحية العديدة وطعمه المميز. يأتي في عبوة فاخرة مناسبة للهدايا.',
        price: 85.0,
        imageUrl: 'https://i.pinimg.com/originals/a0/d0/9d/a0d09d9ddb6c4c3d8cd3d1dd8f5b0ba5.jpg',
        category: 'dates',
        rating: 4.8,
        reviewCount: 120,
        isAvailable: true,
        sizes: ['500 جرام', '1 كيلو', '2 كيلو'],
        colors: [],
        tags: ['تمور', 'عجوة', 'المدينة المنورة', 'هدايا'],
        seller: 'متجر تمور المدينة',
        origin: 'المدينة المنورة',
      ),
      Product(
        id: 'date-2',
        name: 'تمر سكري فاخر',
        description: 'تمر سكري فاخر من مزارع المدينة المنورة، يتميز بحلاوته المعتدلة وقوامه الطري. معبأ في علب خشبية أنيقة.',
        price: 75.0,
        imageUrl: 'https://i.pinimg.com/originals/e9/11/1d/e9111d7945aa9c7d8d3b05aa0a8b1bde.jpg',
        category: 'dates',
        rating: 4.7,
        reviewCount: 95,
        isAvailable: true,
        sizes: ['500 جرام', '1 كيلو', '3 كيلو'],
        colors: [],
        tags: ['تمور', 'سكري', 'المدينة المنورة', 'هدايا'],
        seller: 'متجر تمور المدينة',
        origin: 'المدينة المنورة',
      ),
    ];
  }

  List<Product> _getPerfumes() {
    return [
      Product(
        id: 'perfume-1',
        name: 'عطر المدينة الفاخر',
        description: 'عطر مستوحى من روائح المدينة المنورة العطرة، مزيج من المسك والعنبر والورد. يأتي في عبوة زجاجية فاخرة.',
        price: 350.0,
        imageUrl: 'https://i.pinimg.com/originals/a9/e5/a5/a9e5a51a3c28b35940f420a7a09bb8c3.jpg',
        category: 'perfumes',
        rating: 4.9,
        reviewCount: 85,
        isAvailable: true,
        sizes: ['50 مل', '100 مل'],
        colors: [],
        tags: ['عطور', 'مسك', 'عنبر', 'هدايا'],
        seller: 'متجر العطور الشرقية',
        origin: 'المدينة المنورة',
      ),
      Product(
        id: 'perfume-2',
        name: 'مسك الحرم النبوي',
        description: 'مسك خالص مستوحى من عبق الحرم النبوي الشريف. رائحة فواحة تدوم طويلاً.',
        price: 120.0,
        imageUrl: 'https://i.pinimg.com/originals/b9/a9/a7/b9a9a78e7a4824a712d9297a1b9a5ffe.jpg',
        category: 'perfumes',
        rating: 4.6,
        reviewCount: 65,
        isAvailable: true,
        sizes: ['15 مل', '30 مل'],
        colors: [],
        tags: ['عطور', 'مسك', 'الحرم النبوي', 'هدايا'],
        seller: 'متجر العطور الشرقية',
        origin: 'المدينة المنورة',
      ),
    ];
  }

  List<Product> _getSouvenirs() {
    return [
      Product(
        id: 'souvenir-1',
        name: 'مجسم المسجد النبوي',
        description: 'مجسم دقيق للمسجد النبوي الشريف مصنوع من الرخام عالي الجودة. هدية مثالية وتذكار من زيارة المدينة المنورة.',
        price: 250.0,
        imageUrl: 'https://i.pinimg.com/originals/c5/f0/c3/c5f0c3e8f0dcc7b0d867e2ce5c5c0f8e.jpg',
        category: 'souvenirs',
        rating: 4.7,
        reviewCount: 55,
        isAvailable: true,
        sizes: ['صغير', 'متوسط', 'كبير'],
        colors: ['أبيض', 'ذهبي'],
        tags: ['تذكارات', 'المسجد النبوي', 'هدايا', 'مجسمات'],
        seller: 'متجر هدايا المدينة',
        origin: 'المدينة المنورة',
      ),
    ];
  }

  List<Product> _getClothing() {
    return [
      Product(
        id: 'clothing-1',
        name: 'عباءة مطرزة',
        description: 'عباءة نسائية مطرزة بتصاميم مستوحاة من نقوش المسجد النبوي. مصنوعة من أجود أنواع الأقمشة.',
        price: 450.0,
        imageUrl: 'https://i.pinimg.com/originals/d5/d5/66/d5d566b4e75a0d2c1b1d4d0e1f2a8c7e.jpg',
        category: 'clothing',
        rating: 4.8,
        reviewCount: 42,
        isAvailable: true,
        sizes: ['S', 'M', 'L', 'XL'],
        colors: ['أسود', 'كحلي', 'بني'],
        tags: ['ملابس', 'عباءات', 'مطرزات', 'هدايا'],
        seller: 'متجر أزياء المدينة',
        origin: 'المدينة المنورة',
      ),
    ];
  }

  List<Product> _getFood() {
    return [
      Product(
        id: 'food-1',
        name: 'حلوى المدينة التقليدية',
        description: 'حلوى تقليدية من المدينة المنورة، مصنوعة من السميد والسكر والهيل. تأتي في علبة أنيقة مناسبة للهدايا.',
        price: 65.0,
        imageUrl: 'https://modo3.com/thumbs/fit630x300/198638/1536589897/%D8%B7%D8%B1%D9%8A%D9%82%D8%A9_%D8%B9%D9%85%D9%84_%D8%AD%D9%84%D9%89_%D8%A7%D9%84%D9%85%D8%AF%D9%8A%D9%86%D8%A9.jpg',
        category: 'food',
        rating: 4.5,
        reviewCount: 38,
        isAvailable: true,
        sizes: ['500 جرام', '1 كيلو'],
        colors: [],
        tags: ['حلويات', 'أكل تقليدي', 'هدايا'],
        seller: 'متجر حلويات المدينة',
        origin: 'المدينة المنورة',
      ),
    ];
  }

  List<Product> _getAccessories() {
    return [
      Product(
        id: 'accessory-1',
        name: 'سبحة من خشب العود',
        description: 'سبحة فاخرة مصنوعة من خشب العود الأصلي، مع خرز مطعم بالفضة. تأتي في علبة هدية أنيقة.',
        price: 120.0,
        imageUrl: 'https://i.pinimg.com/originals/e5/e5/e5/e5e5e5e5e5e5e5e5e5e5e5e5e5e5e5e5.jpg',
        category: 'accessories',
        rating: 4.9,
        reviewCount: 75,
        isAvailable: true,
        sizes: ['33 حبة', '99 حبة'],
        colors: ['بني داكن', 'بني فاتح'],
        tags: ['سبح', 'خشب العود', 'هدايا', 'إكسسوارات'],
        seller: 'متجر هدايا المدينة',
        origin: 'المدينة المنورة',
      ),
    ];
  }
}
