import 'dart:convert';
import 'dart:math' as math;
import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import '../models/enhanced_place.dart';
import '../models/place_event.dart';

class EnhancedPlacesService {
  static final EnhancedPlacesService _instance =
      EnhancedPlacesService._internal();
  factory EnhancedPlacesService() => _instance;
  EnhancedPlacesService._internal();

  List<EnhancedPlace> _places = [];
  final List<PlaceEvent> _events = [];
  final List<EnhancedPlaceCategory> _categories = [];
  bool _isLoaded = false;
  bool _eventsLoaded = false;
  bool _categoriesLoaded = false;

  // تحميل البيانات من ملف الـ assets
  Future<List<EnhancedPlace>> loadPlaces() async {
    if (_isLoaded && _places.isNotEmpty) {
      return _places;
    }

    try {
      // تحميل البيانات من ملف destinations.json
      final String jsonString =
          await rootBundle.loadString('assets/destinations.json');
      final List<dynamic> jsonData = json.decode(jsonString);

      _places = [];

      // معالجة البيانات
      if (jsonData.isNotEmpty && jsonData[0]['data'] != null) {
        final List<dynamic> placesData = jsonData[0]['data'];

        for (var placeJson in placesData) {
          try {
            final place = EnhancedPlace.fromJson(placeJson);
            _places.add(place);
          } catch (e) {
            print('خطأ في معالجة مكان: $e');
          }
        }
      }

      // إضافة أماكن إضافية من البيانات المحلية
      _addLocalPlaces();

      _isLoaded = true;
      return _places;
    } catch (e) {
      print('خطأ في تحميل البيانات: $e');
      // إرجاع بيانات افتراضية في حالة الخطأ
      return _getDefaultPlaces();
    }
  }

  // إضافة أماكن إضافية من البيانات المحلية
  void _addLocalPlaces() {
    final additionalPlaces = [
      EnhancedPlace(
        id: 'mihrab_nabawi',
        title: 'المحراب النبوي',
        shortDescription: 'المكان الذي كان يصلي فيه النبي صلى الله عليه وسلم',
        description:
            'المحراب النبوي هو المكان المقدس الذي كان يقف فيه النبي محمد صلى الله عليه وسلم للصلاة إماماً بالمسلمين. يقع في المسجد النبوي ويعتبر من أقدس الأماكن في الإسلام.',
        location: const LatLng(24.4672, 39.6117),
        category: 'prophets-holy-osque',
        images: ['assets/images/places/mihrab1.jpg'],
        categoryInfo: PlaceCategory(
          id: 'prophets-holy-osque',
          name: 'المسجد النبوي',
          slug: 'prophets-holy-osque',
        ),
      ),
      EnhancedPlace(
        id: 'minbar_nabawi',
        title: 'المنبر النبوي',
        shortDescription: 'المنبر الذي كان يخطب عليه النبي صلى الله عليه وسلم',
        description:
            'المنبر النبوي هو المنبر المقدس الذي كان يخطب عليه النبي محمد صلى الله عليه وسلم خطبة الجمعة والعيدين. يقع في المسجد النبوي وله مكانة عظيمة في قلوب المسلمين.',
        location: const LatLng(24.4673, 39.6118),
        category: 'prophets-holy-osque',
        images: [
          'assets/images/places/minbar1.jpg',
          'assets/images/places/minbar2.jpg'
        ],
        categoryInfo: PlaceCategory(
          id: 'prophets-holy-osque',
          name: 'المسجد النبوي',
          slug: 'prophets-holy-osque',
        ),
      ),
      EnhancedPlace(
        id: 'historical_columns',
        title: 'الأعمدة التاريخية',
        shortDescription: 'الأعمدة التي شهدت أحداثاً تاريخية مهمة في عهد النبي',
        description:
            'الأعمدة التاريخية في المسجد النبوي هي أعمدة لها تاريخ عريق، حيث شهدت أحداثاً مهمة في عهد النبي صلى الله عليه وسلم وصحابته الكرام.',
        location: const LatLng(24.4671, 39.6116),
        category: 'prophets-holy-osque',
        images: ['assets/images/places/columns1.jpg'],
        categoryInfo: PlaceCategory(
          id: 'prophets-holy-osque',
          name: 'المسجد النبوي',
          slug: 'prophets-holy-osque',
        ),
      ),
      EnhancedPlace(
        id: 'quba_mosque',
        title: 'مسجد قباء',
        shortDescription: 'أول مسجد بني في الإسلام',
        description:
            'مسجد قباء هو أول مسجد بني في الإسلام، بناه النبي محمد صلى الله عليه وسلم عند وصوله إلى المدينة المنورة. يقع في جنوب المدينة ويستحب زيارته والصلاة فيه.',
        location: const LatLng(24.4380, 39.6158),
        category: 'mosques',
        images: ['assets/images/places/quba1.jpg'],
        categoryInfo: PlaceCategory(
          id: 'mosques',
          name: 'المساجد',
          slug: 'mosques',
        ),
      ),
      EnhancedPlace(
        id: 'qiblatain_mosque',
        title: 'مسجد القبلتين',
        shortDescription:
            'المسجد الذي تم فيه تحويل القبلة من بيت المقدس إلى الكعبة',
        description:
            'مسجد القبلتين هو المسجد التاريخي الذي شهد تحويل القبلة من بيت المقدس إلى الكعبة المشرفة أثناء صلاة الظهر. يقع في شمال غرب المدينة المنورة.',
        location: const LatLng(24.4851, 39.5951),
        category: 'mosques',
        images: ['assets/images/places/qiblatain1.jpg'],
        categoryInfo: PlaceCategory(
          id: 'mosques',
          name: 'المساجد',
          slug: 'mosques',
        ),
      ),
    ];

    _places.addAll(additionalPlaces);
  }

  // بيانات افتراضية في حالة فشل التحميل
  List<EnhancedPlace> _getDefaultPlaces() {
    return [
      EnhancedPlace(
        id: 'default_prophets_mosque',
        title: 'المسجد النبوي',
        shortDescription: 'ثاني أقدس المساجد في الإسلام',
        description:
            'المسجد النبوي هو ثاني أقدس المساجد في الإسلام، ويقع في المدينة المنورة بالمملكة العربية السعودية.',
        location: const LatLng(24.4672, 39.6117),
        category: 'prophets-holy-osque',
        images: ['assets/images/places/mosque3601.jpg'],
        categoryInfo: PlaceCategory(
          id: 'prophets-holy-osque',
          name: 'المسجد النبوي',
          slug: 'prophets-holy-osque',
        ),
      ),
    ];
  }

  // البحث في الأماكن
  List<EnhancedPlace> searchPlaces(String query) {
    if (query.isEmpty) return _places;

    return _places.where((place) {
      return place.title.toLowerCase().contains(query.toLowerCase()) ||
          place.shortDescription.toLowerCase().contains(query.toLowerCase()) ||
          place.description.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  // تصفية الأماكن حسب الفئة
  List<EnhancedPlace> getPlacesByCategory(String category) {
    if (category == 'all') return _places;

    return _places.where((place) => place.category == category).toList();
  }

  // الحصول على مكان بالمعرف
  EnhancedPlace? getPlaceById(String id) {
    try {
      return _places.firstWhere((place) => place.id == id);
    } catch (e) {
      return null;
    }
  }

  // الحصول على الأماكن القريبة
  List<EnhancedPlace> getNearbyPlaces(LatLng location,
      {double radiusKm = 5.0}) {
    return _places.where((place) {
      final distance = _calculateDistance(location, place.location);
      return distance <= radiusKm;
    }).toList();
  }

  // حساب المسافة بين نقطتين
  double _calculateDistance(LatLng point1, LatLng point2) {
    const double earthRadius = 6371; // نصف قطر الأرض بالكيلومتر

    final double lat1Rad = point1.latitude * (3.14159 / 180);
    final double lat2Rad = point2.latitude * (3.14159 / 180);
    final double deltaLatRad =
        (point2.latitude - point1.latitude) * (3.14159 / 180);
    final double deltaLngRad =
        (point2.longitude - point1.longitude) * (3.14159 / 180);

    final double a = math.sin(deltaLatRad / 2) * math.sin(deltaLatRad / 2) +
        math.cos(lat1Rad) *
            math.cos(lat2Rad) *
            math.sin(deltaLngRad / 2) *
            math.sin(deltaLngRad / 2);
    final double c = 2 * math.atan2(math.sqrt(a), math.sqrt(1 - a));

    return earthRadius * c;
  }

  // إحصائيات الأماكن
  Map<String, int> getPlacesStatistics() {
    final stats = <String, int>{};

    for (final place in _places) {
      stats[place.category] = (stats[place.category] ?? 0) + 1;
    }

    stats['total'] = _places.length;
    stats['with_multimedia'] = _places.where((p) => p.hasMultimedia).length;
    stats['with_images'] = _places.where((p) => p.images.isNotEmpty).length;

    return stats;
  }

  // تحميل الأحداث من ملف الـ assets
  Future<List<PlaceEvent>> loadEvents() async {
    if (_eventsLoaded && _events.isNotEmpty) {
      return _events;
    }

    try {
      final String jsonString =
          await rootBundle.loadString('assets/events.json');
      final List<dynamic> jsonData = json.decode(jsonString);

      _events.clear();

      for (var eventJson in jsonData) {
        try {
          final event = PlaceEvent.fromJson(eventJson);
          _events.add(event);
        } catch (e) {
          print('خطأ في معالجة حدث: $e');
        }
      }

      _eventsLoaded = true;
      return _events;
    } catch (e) {
      print('خطأ في تحميل الأحداث: $e');
      return [];
    }
  }

  // تحميل الفئات من بيانات الأماكن
  Future<List<EnhancedPlaceCategory>> loadCategories() async {
    if (_categoriesLoaded && _categories.isNotEmpty) {
      return _categories;
    }

    // تأكد من تحميل الأماكن أولاً
    if (!_isLoaded) {
      await loadPlaces();
    }

    try {
      _categories.clear();

      // إضافة فئة "الكل"
      _categories.addAll(EnhancedPlaceCategory.getDefaultCategories());

      // استخراج الفئات الفريدة من الأماكن
      final Set<String> uniqueCategories = {};
      final Map<String, String> categoryNames = {};

      for (final place in _places) {
        if (place.category.isNotEmpty) {
          uniqueCategories.add(place.category);
          categoryNames[place.category] = place.categoryInfo.name;
        }
      }

      // إنشاء فئات محسنة
      for (final categorySlug in uniqueCategories) {
        final categoryName = categoryNames[categorySlug] ?? categorySlug;
        final enhancedCategory =
            EnhancedPlaceCategory.fromCategorySlug(categorySlug, categoryName);
        _categories.add(enhancedCategory);
      }

      _categoriesLoaded = true;
      return _categories;
    } catch (e) {
      print('خطأ في تحميل الفئات: $e');
      return EnhancedPlaceCategory.getDefaultCategories();
    }
  }

  // الحصول على الأحداث المرتبطة بمكان معين
  List<PlaceEvent> getEventsForPlace(String placeId) {
    return _events
        .where((event) => event.placeId == placeId || event.placeId == 'all')
        .toList();
  }

  // الحصول على الأحداث النشطة
  List<PlaceEvent> getActiveEvents() {
    return _events.where((event) => event.isCurrentlyActive).toList();
  }

  // الحصول على الأحداث القادمة
  List<PlaceEvent> getUpcomingEvents() {
    return _events.where((event) => event.isUpcoming).toList();
  }

  // الحصول على الأحداث حسب النوع
  List<PlaceEvent> getEventsByType(EventType type) {
    return _events.where((event) => event.type == type).toList();
  }

  // البحث في الأحداث
  List<PlaceEvent> searchEvents(String query) {
    if (query.isEmpty) return _events;

    return _events.where((event) {
      return event.title.toLowerCase().contains(query.toLowerCase()) ||
          event.description.toLowerCase().contains(query.toLowerCase()) ||
          event.shortDescription.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  // تحديث البيانات
  Future<void> refreshPlaces() async {
    _isLoaded = false;
    _eventsLoaded = false;
    _categoriesLoaded = false;
    _places.clear();
    _events.clear();
    _categories.clear();
    await loadPlaces();
    await loadEvents();
    await loadCategories();
  }
}
