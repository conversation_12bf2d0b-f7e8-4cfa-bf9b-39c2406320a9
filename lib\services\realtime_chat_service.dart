import 'package:dio/dio.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

class RealtimeChatService {
  static const String _baseUrl = 'https://api.openai.com/v1';
  static final _dio = Dio(BaseOptions(
    baseUrl: _baseUrl,
    contentType: 'application/sdp',
    responseType: ResponseType.plain,
  ));

  static Future<String> connectWebRTC(String sdp) async {
    final uri = Uri.parse('$_baseUrl/realtime').replace(queryParameters: {
      'model': 'gpt-4o-realtime-preview-2024-12-17',
      'instructions': 'أنت وٍفادة ، مساعدة افتراضية ودليل سياحي شامل لزوار المدينة المنورة.يمكنك التحدث بكل اللغات بشكل جيد وعند سؤالك بأي لغة      '
          'ابدأي بتحيت الاسلام في بداية المحادثة ورحبي في ضيف الرحمن في بداية المحادثة فقط وليس بعد كل رد ، ثم أضيفي دعاءً طيبًا للزائر. '
          'قدمي معلومات تفصيلية عن المعالم الإسلامية، التاريخية، والثقافية في المدينة المنورة، مثل المسجد النبوي، البقيع، مسجد قباء، والمساجد التاريخية. '
          'وفري إرشادات حول المواصلات، الفنادق، الخدمات الصحية، وأي شيء يحتاجه الزائر خلال إقامته. '
          'اجعلي تفاعلك شخصيًا، واسألي الزائر عن هدف زيارته لتقديم معلومات تناسب احتياجاته. '
          'يمكنك إضافة لمسات من روح الدعابة باحترام إذا كان ذلك مناسبًا، ولكن حافظي دائمًا على أسلوب لبق يعكس كرم المدينة المنورة.',
      'voice': 'alloy',
    });
    try {
      final response = await _dio.postUri(
        uri,
        data: sdp,
        options: Options(
          headers: {
            'Authorization': 'Bearer ${dotenv.env['OPENAI_API_KEY']}',
          },
        ),
      );

      return response.data;
    } on DioException catch (e) {
      print('OpenAI Response: ${e.response?.data}');
      throw Exception('Failed to connect to OpenAI: ${e.response?.statusCode}');
    }
  }
}
