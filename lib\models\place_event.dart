class PlaceEvent {
  final String id;
  final String title;
  final String description;
  final String shortDescription;
  final DateTime startDate;
  final DateTime endDate;
  final String? image;
  final String? location;
  final String category;
  final String placeId;
  final EventType type;
  final bool isActive;
  final Map<String, String> additionalInfo;

  PlaceEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.shortDescription,
    required this.startDate,
    required this.endDate,
    this.image,
    this.location,
    required this.category,
    required this.placeId,
    required this.type,
    this.isActive = true,
    this.additionalInfo = const {},
  });

  factory PlaceEvent.fromJson(Map<String, dynamic> json) {
    return PlaceEvent(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      shortDescription: json['short_description'] ?? '',
      startDate: DateTime.tryParse(json['start_date'] ?? '') ?? DateTime.now(),
      endDate: DateTime.tryParse(json['end_date'] ?? '') ?? DateTime.now().add(const Duration(hours: 2)),
      image: json['image'],
      location: json['location'],
      category: json['category'] ?? 'general',
      placeId: json['place_id'] ?? '',
      type: EventType.fromString(json['type'] ?? 'historical'),
      isActive: json['is_active'] ?? true,
      additionalInfo: Map<String, String>.from(json['additional_info'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'short_description': shortDescription,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'image': image,
      'location': location,
      'category': category,
      'place_id': placeId,
      'type': type.name,
      'is_active': isActive,
      'additional_info': additionalInfo,
    };
  }

  // التحقق من كون الحدث نشط حالياً
  bool get isCurrentlyActive {
    final now = DateTime.now();
    return isActive && now.isAfter(startDate) && now.isBefore(endDate);
  }

  // التحقق من كون الحدث قادم
  bool get isUpcoming {
    final now = DateTime.now();
    return isActive && now.isBefore(startDate);
  }

  // التحقق من كون الحدث انتهى
  bool get isFinished {
    final now = DateTime.now();
    return now.isAfter(endDate);
  }

  // مدة الحدث
  Duration get duration {
    return endDate.difference(startDate);
  }

  // الوقت المتبقي للحدث
  Duration? get timeUntilStart {
    final now = DateTime.now();
    if (now.isBefore(startDate)) {
      return startDate.difference(now);
    }
    return null;
  }

  // الوقت المتبقي لانتهاء الحدث
  Duration? get timeUntilEnd {
    final now = DateTime.now();
    if (now.isBefore(endDate)) {
      return endDate.difference(now);
    }
    return null;
  }

  // حالة الحدث
  EventStatus get status {
    final now = DateTime.now();
    if (!isActive) return EventStatus.cancelled;
    if (now.isBefore(startDate)) return EventStatus.upcoming;
    if (now.isAfter(endDate)) return EventStatus.finished;
    return EventStatus.active;
  }

  // لون الحدث حسب النوع
  String get color {
    switch (type) {
      case EventType.historical:
        return '#D4A574';
      case EventType.religious:
        return '#2E8B57';
      case EventType.cultural:
        return '#4169E1';
      case EventType.educational:
        return '#9B59B6';
      case EventType.seasonal:
        return '#FF6B35';
      case EventType.special:
        return '#E74C3C';
      default:
        return '#4F908E';
    }
  }

  // أيقونة الحدث حسب النوع
  String get icon {
    switch (type) {
      case EventType.historical:
        return '🏛️';
      case EventType.religious:
        return '🕌';
      case EventType.cultural:
        return '🎭';
      case EventType.educational:
        return '📚';
      case EventType.seasonal:
        return '🌟';
      case EventType.special:
        return '✨';
      default:
        return '📅';
    }
  }
}

enum EventType {
  historical,
  religious,
  cultural,
  educational,
  seasonal,
  special;

  static EventType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'historical':
        return EventType.historical;
      case 'religious':
        return EventType.religious;
      case 'cultural':
        return EventType.cultural;
      case 'educational':
        return EventType.educational;
      case 'seasonal':
        return EventType.seasonal;
      case 'special':
        return EventType.special;
      default:
        return EventType.historical;
    }
  }

  String get displayName {
    switch (this) {
      case EventType.historical:
        return 'تاريخي';
      case EventType.religious:
        return 'ديني';
      case EventType.cultural:
        return 'ثقافي';
      case EventType.educational:
        return 'تعليمي';
      case EventType.seasonal:
        return 'موسمي';
      case EventType.special:
        return 'خاص';
    }
  }
}

enum EventStatus {
  upcoming,
  active,
  finished,
  cancelled;

  String get displayName {
    switch (this) {
      case EventStatus.upcoming:
        return 'قادم';
      case EventStatus.active:
        return 'نشط';
      case EventStatus.finished:
        return 'انتهى';
      case EventStatus.cancelled:
        return 'ملغي';
    }
  }

  String get color {
    switch (this) {
      case EventStatus.upcoming:
        return '#4169E1';
      case EventStatus.active:
        return '#2E8B57';
      case EventStatus.finished:
        return '#95A5A6';
      case EventStatus.cancelled:
        return '#E74C3C';
    }
  }
}
