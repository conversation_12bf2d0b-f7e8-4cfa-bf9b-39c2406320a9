import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:iconsax/iconsax.dart';
import 'package:provider/provider.dart';
import 'package:wiffada/providers/language_provider.dart';
import 'package:wiffada/utils/app_localizations.dart';

class BuildSeerahSection extends StatelessWidget {
  const BuildSeerahSection({super.key});

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);
    final translations = AppLocalizations(languageProvider.currentLanguage);
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF9B59B6).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Iconsax.book_1,
                  color: Color(0xFF9B59B6),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'السيرة النبوية التفاعلية',
                  style: GoogleFonts.ibmPlexSansArabic(
                    fontSize: screenWidth < 360 ? 18 : 20,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF2C3E50),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ).animate().fadeIn(duration: const Duration(milliseconds: 600)),

          const SizedBox(height: 8),
          Text(
            'تعلم سيرة النبي ﷺ بطريقة تفاعلية ومبتكرة',
            style: GoogleFonts.ibmPlexSansArabic(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ).animate().fadeIn(delay: const Duration(milliseconds: 200)),

          const SizedBox(height: 16),

          // Main card
          GestureDetector(
            onTap: () => Navigator.pushNamed(context, '/seerah'),
            child: Container(
              width: double.infinity,
              constraints: BoxConstraints(
                minHeight: screenWidth < 360 ? 140 : 160,
                maxHeight: screenWidth < 360 ? 160 : 180,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF9B59B6),
                    Color(0xFF8E44AD),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF9B59B6).withValues(alpha: 0.3),
                    offset: const Offset(0, 8),
                    blurRadius: 20,
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // Background pattern
                  Positioned.fill(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: Container(
                        decoration: const BoxDecoration(
                          image: DecorationImage(
                            image:
                                AssetImage('assets/images/backgrounds/3.jpg'),
                            fit: BoxFit.cover,
                            opacity: 0.2,
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Content
                  Padding(
                    padding: EdgeInsets.all(screenWidth < 360 ? 16 : 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              padding:
                                  EdgeInsets.all(screenWidth < 360 ? 8 : 10),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                Iconsax.book_1,
                                color: Colors.white,
                                size: screenWidth < 360 ? 18 : 20,
                              ),
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: screenWidth < 360 ? 8 : 10,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Text(
                                'جديد',
                                style: GoogleFonts.ibmPlexSansArabic(
                                  fontSize: screenWidth < 360 ? 10 : 11,
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Text(
                                'السيرة النبوية التفاعلية',
                                style: GoogleFonts.ibmPlexSansArabic(
                                  fontSize: screenWidth < 360 ? 16 : 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: screenWidth < 360 ? 4 : 6),
                              Text(
                                'اكتشف حياة النبي ﷺ بطريقة تفاعلية',
                                style: GoogleFonts.ibmPlexSansArabic(
                                  fontSize: screenWidth < 360 ? 11 : 12,
                                  color: Colors.white.withValues(alpha: 0.9),
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: screenWidth < 360 ? 6 : 8),
                              Row(
                                children: [
                                  Text(
                                    'ابدأ الرحلة',
                                    style: GoogleFonts.ibmPlexSansArabic(
                                      fontSize: screenWidth < 360 ? 12 : 14,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                  SizedBox(width: screenWidth < 360 ? 4 : 6),
                                  Icon(
                                    Icons.arrow_forward,
                                    color: Colors.white,
                                    size: screenWidth < 360 ? 14 : 16,
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          )
              .animate()
              .fadeIn(delay: const Duration(milliseconds: 400))
              .slideY(begin: 0.3, end: 0),

          const SizedBox(height: 16),

          // Quick access buttons
          LayoutBuilder(
            builder: (context, constraints) {
              final buttonWidth = (constraints.maxWidth - 24) /
                  3; // 3 buttons with 12px spacing
              return Row(
                children: [
                  SizedBox(
                    width: buttonWidth,
                    child: _buildQuickAccessButton(
                      context,
                      'الخط الزمني',
                      Iconsax.calendar,
                      const Color(0xFF3498DB),
                      '/seerah/timeline',
                      screenWidth,
                    ),
                  ),
                  const SizedBox(width: 12),
                  SizedBox(
                    width: buttonWidth,
                    child: _buildQuickAccessButton(
                      context,
                      'الصحابة',
                      Iconsax.people,
                      const Color(0xFF27AE60),
                      '/seerah/companions',
                      screenWidth,
                    ),
                  ),
                  const SizedBox(width: 12),
                  SizedBox(
                    width: buttonWidth,
                    child: _buildQuickAccessButton(
                      context,
                      'تفاعلي',
                      Iconsax.video_play,
                      const Color(0xFFE74C3C),
                      '/seerah/interactive',
                      screenWidth,
                    ),
                  ),
                ],
              );
            },
          )
              .animate()
              .fadeIn(delay: const Duration(milliseconds: 600))
              .slideY(begin: 0.3, end: 0),
        ],
      ),
    );
  }

  Widget _buildQuickAccessButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    String route,
    double screenWidth,
  ) {
    return GestureDetector(
      onTap: () => Navigator.pushNamed(context, route),
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: screenWidth < 360 ? 12 : 14,
          horizontal: screenWidth < 360 ? 8 : 10,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              offset: const Offset(0, 2),
              blurRadius: 8,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(screenWidth < 360 ? 8 : 10),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: color,
                size: screenWidth < 360 ? 16 : 18,
              ),
            ),
            SizedBox(height: screenWidth < 360 ? 6 : 8),
            Text(
              title,
              style: GoogleFonts.ibmPlexSansArabic(
                fontSize: screenWidth < 360 ? 10 : 11,
                fontWeight: FontWeight.bold,
                color: const Color(0xFF2C3E50),
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
